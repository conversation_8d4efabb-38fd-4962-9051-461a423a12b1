import { View, Text, TouchableOpacity, StyleSheet, Linking } from 'react-native';
import React from 'react';
import { Colors } from '../../constants/Colors';

export default function FlightInfo({ flightData }) {
  console.log('FlightInfo data:', flightData);

  // Assuming flightData is passed as tripPlan.trip.flights from the database
  const flight = Array.isArray(flightData) && flightData.length > 0 ? flightData[0] : {};
  const isEmpty = !flightData || (Array.isArray(flightData) && flightData.length === 0);

  const handleBookingPress = async (url) => {
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        console.error(`Cannot open URL: ${url}`);
      }
    } catch (error) {
      console.error('Error opening URL:', error);
    }
  };

  if (isEmpty) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>✈️ Flights</Text>
        <Text style={styles.noDataText}>No flight information available</Text>
      </View>
    );
  }

  return (
    <View style={styles.wrapper}>
      <View style={styles.container}>
        <View style={styles.headerRow}>
          <Text style={styles.title}>🛩️ Flights</Text>
          {flight?.booking_url && (
            <TouchableOpacity
              style={styles.button}
              onPress={() => handleBookingPress(flight.booking_url)}
              activeOpacity={0.7}
              accessibilityRole="button"
              accessibilityLabel="Book flight"
            >
              <Text style={styles.buttonText}>Book Here</Text>
            </TouchableOpacity>
          )}
        </View>
        <View style={styles.flightDetails}>
          <Text style={styles.detailText}>Airline: {flight?.airline || 'Unknown Airline'}</Text>
          <Text style={styles.detailText}>Flight: {flight?.flight_number || 'N/A'}</Text>
          <Text style={styles.detailText}>Price: {flight?.price || 'N/A'}</Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  wrapper: {
    marginTop: 20,
  },
  container: {
    borderWidth: 1,
    borderColor: Colors.LIGHT_GRAY,
    padding: 15,
    borderRadius: 15,
    backgroundColor: Colors.WHITE,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  title: {
    fontFamily: 'outfit-bold',
    fontSize: 22,
    color: Colors.DARK,
  },
  noDataText: {
    fontFamily: 'outfit',
    fontSize: 16,
    color: Colors.GRAY,
    textAlign: 'center',
  },
  flightDetails: {
    gap: 8,
  },
  detailText: {
    fontFamily: 'outfit',
    fontSize: 16,
    color: Colors.DARK_GRAY,
  },
  button: {
    paddingVertical: 5,
    paddingHorizontal: 10,
    backgroundColor: Colors.PRIMARY,
    borderRadius: 7,
  },
  buttonText: {
    fontFamily: 'outfit-medium',
    fontSize: 16,
    color: Colors.WHITE,
    textAlign: 'center',
  },
});
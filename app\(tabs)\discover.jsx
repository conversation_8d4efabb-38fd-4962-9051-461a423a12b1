import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import React, { useEffect, useState } from "react";
import { useRouter } from "expo-router";
import { Colors } from "./../../constants/Colors";
import Ionicons from "@expo/vector-icons/Ionicons";
import { collection, query, where, getDocs } from "firebase/firestore";
import { auth, db } from "./../../configs/FirebaseConfig";

export default function Discover() {
  const router = useRouter();
  const [tripCount, setTripCount] = useState(0);

  useEffect(() => {
    const fetchTrips = async () => {
      const user = auth.currentUser;
      if (!user) return;

      try {
        const q = query(collection(db, "UserTrips"), where("userEmail", "==", user.email));
        const snapshot = await getDocs(q);
        setTripCount(snapshot.size);
      } catch (error) {
        console.error("Failed to fetch trips:", error.message);
        setTripCount(0);
      }
    };

    fetchTrips();
  }, []);

  return (
    <View style={styles.container}>
      {/* Header */}
    

      {/* Main UI Based on Trip Count */}
      {tripCount > 0 ? (
        <View style={styles.card}>
          <Ionicons name="earth-outline" size={50} color={Colors.PRIMARY} />
          <Text style={styles.title}>
            You’ve Planned {tripCount} {tripCount === 1 ? "Trip" : "Trips"}!
          </Text>
          <Text style={styles.subtitle}>
            Ready to plan your next adventure? Let’s explore more places.
          </Text>

          <TouchableOpacity
            onPress={() => router.push("/create-trip/search-place")}
            style={styles.primaryButton}
            activeOpacity={0.8}
          >
            <Ionicons name="add-circle-outline" size={20} color={Colors.WHITE} />
            <Text style={styles.buttonText}>Create New Trip</Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => router.push("/mytrip")}
            style={styles.secondaryButton}
            activeOpacity={0.8}
          >
            <Ionicons name="list-outline" size={20} color={Colors.WHITE} />
            <Text style={styles.buttonText}>View My Trips</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <View style={styles.emptyCard}>
          <Ionicons name="alert-circle-outline" size={60} color={Colors.GRAY} />
          <Text style={styles.title}>No Trips Found</Text>
          <Text style={styles.subtitle}>
            Your journey starts from the "My Trips" tab. Create your first trip there!
          </Text>

          <TouchableOpacity
            onPress={() => router.push("/(tabs)/mytrip")}
            style={styles.primaryButton}
            activeOpacity={0.8}
          >
            <Ionicons name="navigate-outline" size={20} color={Colors.WHITE} />
            <Text style={styles.buttonText}>Go to My Trips</Text>
          </TouchableOpacity>
        </View>
      )}

      <Text style={styles.footer}>
        “Adventure is out there. Go find it.”
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    paddingTop: 60,
    backgroundColor: Colors.WHITE,
  },
  header: {
    alignItems: "center",
    marginBottom: 30,
  },
  headerText: {
    fontFamily: "outfit-bold",
    fontSize: 24,
    color: Colors.PRIMARY,
    marginTop: 8,
  },
  card: {
    backgroundColor: Colors.WHITE,
    padding: 20,
    borderRadius: 18,
    alignItems: "center",
    gap: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 4,
  },
  emptyCard: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    gap: 18,
  },
  title: {
    fontFamily: "outfit-medium",
    fontSize: 20,
    color: Colors.PRIMARY,
    textAlign: "center",
  },
  subtitle: {
    fontFamily: "outfit",
    fontSize: 15,
    color: Colors.GRAY,
    textAlign: "center",
    maxWidth: "85%",
  },
  primaryButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 12,
    marginTop: 10,
  },
  secondaryButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.GRAY,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 12,
    marginTop: 8,
  },
  buttonText: {
    fontFamily: "outfit-medium",
    color: Colors.WHITE,
    fontSize: 15,
    marginLeft: 8,
  },
  footer: {
    marginTop: 30,
    textAlign: "center",
    fontFamily: "outfit",
    fontSize: 14,
    color: Colors.GRAY,
    fontStyle: "italic",
  },
});
import { View, Text, FlatList, TouchableOpacity, Dimensions } from 'react-native';
import React, { useContext, useEffect, useState } from 'react';
import { useNavigation, router } from 'expo-router';
import { Colors } from './../../constants/Colors';
import { SelectTravelerList } from './../../constants/Options';
import OptionCard from './../../components/CreateTrip/OptionCard';
import { CreateTripContext } from './../../Context/CreateTripContext';

const { width, height } = Dimensions.get('window');

export default function SelectTraveler() {
  const navigation = useNavigation();
  const [selectedTraveler, setSelectedTraveler] = useState(null);
  const { tripData, setTripData } = useContext(CreateTripContext);

  useEffect(() => {
    navigation.setOptions({
      headerShown: true,
      headerTransparent: true,
      headerTitle: '',
      headerStyle: { borderBottomWidth: 0 },
    });
  }, []);

  useEffect(() => {
    if (selectedTraveler) {
      console.log('Updating tripData with traveler:', selectedTraveler);
      setTripData({ ...tripData, traveler: selectedTraveler });
    }
  }, [selectedTraveler]);

  console.log('Current selectedTraveler:', selectedTraveler);

  return (
    <View style={{
      flex: 1,
      backgroundColor: Colors.WHITE,
      paddingHorizontal: width * 0.05,
      paddingTop: height * 0.1,
    }}>
      <Text style={{
        fontSize: Math.min(width * 0.08, 30),
        fontFamily: 'outfit-bold',
        marginBottom: height * 0.03,
        color: Colors.DARK,
      }}>
        Who's Traveling
      </Text>

      <View style={{
        flex: 1,
        marginBottom: height * 0.02,
      }}>
        <Text style={{
          fontFamily: 'outfit-bold',
          fontSize: Math.min(width * 0.06, 23),
          color: Colors.GRAY,
          marginBottom: height * 0.02,
        }}>
          Choose Your Travelers
        </Text>

        <FlatList
          data={SelectTravelerList}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: height * 0.15 }}
          keyExtractor={(item) => item.id.toString()}
          renderItem={({ item }) => (
            <TouchableOpacity
              onPress={() => {
                console.log('Selected traveler:', item);
                setSelectedTraveler(item);
              }}
              style={{ marginVertical: height * 0.015 }}
            >
                <OptionCard
                    option={item}
                    selectedOption={selectedTraveler}
                    onSelect={(selected) => {
                    console.log('Selected traveler:', selected); // Debug
                    setSelectedTraveler(selected);
                    }}
                    style={{
                        padding: width * 0.03,
                        borderRadius: 10,
                        backgroundColor:
                        selectedTraveler?.id === item.id
                            ? Colors.PRIMARY + '20'
                            : Colors.WHITE,
                        elevation: 2,
                        shadowColor: '#000',
                        shadowOffset: { width: 0, height: 2 },
                        shadowOpacity: 0.1,
                        shadowRadius: 4,
                    }}
                />
            </TouchableOpacity>
          )}
        />
      </View>

      <TouchableOpacity
        onPress={() => {
          if (selectedTraveler) {
            router.push('/create-trip/select-dates');
          }
        }}
        style={{
          position: 'absolute',
          bottom: height * 0.03,
          left: width * 0.05,
          right: width * 0.05,
          paddingVertical: height * 0.02,
          backgroundColor: selectedTraveler ? Colors.PRIMARY : Colors.GRAY,
          borderRadius: 15,
          elevation: 5,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.2,
          shadowRadius: 4,
        }}
        disabled={!selectedTraveler}
      >
        <Text style={{
          textAlign: 'center',
          color: Colors.WHITE,
          fontFamily: 'outfit-medium',
          fontSize: Math.min(width * 0.05, 20),
        }}>
          Continue
        </Text>
      </TouchableOpacity>
    </View>
  );
}
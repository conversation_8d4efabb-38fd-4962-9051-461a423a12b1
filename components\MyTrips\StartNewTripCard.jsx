import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import React from 'react';
import Ionicons from '@expo/vector-icons/Ionicons';
import { Colors } from './../../constants/Colors';
import { useRouter } from 'expo-router';

export default function StartNewTripCard() {
  const router = useRouter();

  const handleStartNewTrip = () => {
    router.push('/create-trip/search-place');
  };

  return (
    <View style={styles.container}>
      <Ionicons 
        name="location-sharp" 
        size={30} 
        color={Colors.DARK} 
        style={styles.icon}
        accessibilityLabel="Location icon"
      />
      
      <Text style={styles.title}>
        No Trip Plans Yet!!!
      </Text>

      <Text style={styles.subtitle}>
        Time to plan your next adventure! Let's get started below.
      </Text>

      <TouchableOpacity
        onPress={handleStartNewTrip}
        style={styles.button}
        activeOpacity={0.7}
        accessibilityRole="button"
        accessibilityLabel="Start a new trip"
      >
        <Text style={styles.buttonText}>
          Start a New Trip
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    marginTop: 50,
    alignItems: 'center',
    gap: 20,
    backgroundColor: Colors.WHITE,
    borderRadius: 15,
    marginHorizontal: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 3,
  },
  icon: {
    marginBottom: 5,
  },
  title: {
    fontSize: 26,
    fontFamily: 'outfit-medium',
    color: Colors.DARK,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 18,
    fontFamily: 'outfit',
    textAlign: 'center',
    color: Colors.GRAY,
    lineHeight: 24,
    paddingHorizontal: 10,
  },
  button: {
    paddingVertical: 15,
    paddingHorizontal: 35,
    backgroundColor: Colors.PRIMARY,
    borderRadius: 15,
    marginTop: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 2,
  },
  buttonText: {
    color: Colors.WHITE,
    fontFamily: 'outfit-medium',
    fontSize: 18,
    textAlign: 'center',
  },
});
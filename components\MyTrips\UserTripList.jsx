import { View, Text, TouchableOpacity, Image, ActivityIndicator } from 'react-native';
import React, { useState, useEffect } from 'react';
import { Colors } from './../../constants/Colors';
import moment from 'moment';
import { useRouter } from 'expo-router';
import UserTripCard from './UserTripCard';
import { db } from './../../configs/FirebaseConfig';
import { collection, deleteDoc, doc } from 'firebase/firestore';

export default function UserTripList({ userTrips }) {
  const router = useRouter();
  const [imageUrl, setImageUrl] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const latestTrip = userTrips?.[0];
  console.log('UserTrips received:', userTrips);

  // Parse tripData if it’s a string
  const latestTripData = typeof latestTrip?.tripData === 'string'
    ? JSON.parse(latestTrip?.tripData || '{}')
    : latestTrip?.tripData || {};
  const location = latestTripData?.locationInfo?.name || 'Unknown Destination'; // Use locationInfo.name from database

  // Format the startDate
  let formattedStartDate;
  if (latestTripData?.startDate) {
    if (latestTripData.startDate.toDate) { // Handle Firebase Timestamp
      formattedStartDate = moment(latestTripData.startDate.toDate()).format('DD MMM YYYY');
    } else {
      formattedStartDate = moment(latestTripData.startDate).format('DD MMM YYYY');
    }
  } else {
    formattedStartDate = moment(latestTrip?.createdAt).format('DD MMM YYYY') || 'Date not available'; // Fallback to createdAt
  }

  useEffect(() => {
    const fetchUnsplashImage = async () => {
      const unsplashAccessKey = process.env.EXPO_PUBLIC_UNSPLASH_API_KEY;
      if (!location || !unsplashAccessKey || location === 'Unknown Destination') return;
      setIsLoading(true);
      try {
        const response = await fetch(
          `https://api.unsplash.com/photos/random?query=${encodeURIComponent(location)}&client_id=${unsplashAccessKey}`
        );
        if (!response.ok) throw new Error('Unsplash API request failed');
        const data = await response.json();
        setImageUrl(data?.urls?.regular || null);
      } catch (error) {
        console.error('Error fetching Unsplash image:', error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchUnsplashImage();
  }, [location]);

  const handleSeePlan = () => {
    if (latestTrip) {
      const tripString = JSON.stringify(latestTrip);
      router.push({
        pathname: '/trip-details',
        params: { trip: tripString },
      });
    }
  };

  const handleDeleteTrip = async (tripId) => {
    try {
      await deleteDoc(doc(db, 'trips', tripId));
      console.log('Trip deleted:', tripId);
      // Optionally, you could trigger a refresh of userTrips here
    } catch (error) {
      console.error('Error deleting trip:', error);
    }
  };

  return (
    <View style={{ padding: 10 }}>
      {/* Latest Trip Section */}
      {latestTrip && (
        <View style={{ marginBottom: 20 }}>
          <View style={{ position: 'relative' }}>
            {isLoading ? (
              <View
                style={{
                  width: '100%',
                  height: 240,
                  borderRadius: 15,
                  backgroundColor: Colors.LIGHT_GRAY,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <ActivityIndicator size="large" color={Colors.PRIMARY} />
              </View>
            ) : (
              <Image
                source={imageUrl ? { uri: imageUrl } : require('./../../assets/images/icon.png')}
                style={{
                  width: '100%',
                  height: 240,
                  borderRadius: 15,
                  resizeMode: imageUrl ? 'cover' : 'contain',
                }}
              />
            )}
          </View>

          <View style={{ marginTop: 15 }}>
            <Text style={{ fontFamily: 'outfit-medium', fontSize: 24, color: Colors.DARK }}>
              {location}
            </Text>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginTop: 8 }}>
              <Text style={{ fontFamily: 'outfit', fontSize: 17, color: Colors.GRAY }}>
                {formattedStartDate}
              </Text>
              <Text style={{ fontFamily: 'outfit', fontSize: 17, color: Colors.GRAY }}>
                {latestTripData?.traveler?.icon || '🧳'} {latestTripData?.traveler?.title || 'Traveler info N/A'}
              </Text>
            </View>
            <TouchableOpacity
              onPress={handleSeePlan}
              style={{
                backgroundColor: Colors.PRIMARY,
                paddingVertical: 15,
                paddingHorizontal: 20,
                borderRadius: 15,
                marginTop: 15,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.2,
                shadowRadius: 4,
                elevation: 3,
              }}
            >
              <Text style={{ color: Colors.WHITE, textAlign: 'center', fontFamily: 'outfit-medium', fontSize: 16 }}>
                See Your Plan
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* All Trips List */}
      <View>
        {userTrips?.length > 0 ? (
          userTrips.map((trip, index) => (
            <UserTripCard
              trip={trip}
              key={trip.docId || index} // Use docId from database
              onDelete={() => handleDeleteTrip(trip.docId)}
            />
          ))
        ) : (
          <Text style={{ fontFamily: 'outfit', fontSize: 16, color: Colors.GRAY, textAlign: 'center', marginTop: 20 }}>
            No trips available
          </Text>
        )}
      </View>
    </View>
  );
}
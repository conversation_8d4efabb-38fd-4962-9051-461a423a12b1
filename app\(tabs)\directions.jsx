import { View, Text, TextInput, TouchableOpacity, StyleSheet, ActivityIndicator, Dimensions, Image, ScrollView, SafeAreaView, Platform, Alert } from 'react-native';
import React, { useState, useEffect } from 'react';
import { Colors } from '../../constants/Colors';
import MapboxClient from '@mapbox/mapbox-sdk';
import mbxDirections from '@mapbox/mapbox-sdk/services/directions';
import mbxGeocoding from '@mapbox/mapbox-sdk/services/geocoding';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';

const { width, height } = Dimensions.get('window');

export default function Directions() {
  const [from, setFrom] = useState('');
  const [to, setTo] = useState('');
  const [loading, setLoading] = useState(false);
  const [directions, setDirections] = useState(null);
  const [error, setError] = useState('');
  const [mapImageUrl, setMapImageUrl] = useState('');
  const [fromCoords, setFromCoords] = useState(null);
  const [toCoords, setToCoords] = useState(null);
  const [routeSteps, setRouteSteps] = useState([]);
  const [mapZoom, setMapZoom] = useState(14);
  const [showAllSteps, setShowAllSteps] = useState(false);
  const [mapLoadFailed, setMapLoadFailed] = useState(false);
  const [userLocation, setUserLocation] = useState(null);
  const [usingCurrentLocation, setUsingCurrentLocation] = useState(false);
  const [locationPermission, setLocationPermission] = useState(null);

  // Store the Mapbox token directly for easier access
  const mapboxToken = process.env.EXPO_PUBLIC_MAPBOX_API_TOKEN;

  // Verify token is available
  useEffect(() => {
    if (!mapboxToken) {
      console.error("Mapbox token not available. Check your environment configuration.");
      setError('Mapbox API token is not configured. Please check your environment settings.');
      setMapLoadFailed(true);
    }
  }, [mapboxToken]);

  const baseClient = MapboxClient({ accessToken: mapboxToken });
  const directionsClient = mbxDirections(baseClient);
  const geocodingClient = mbxGeocoding(baseClient);

  // Request location permission and get user's current location
  useEffect(() => {
    const getLocationPermission = async () => {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        setLocationPermission(status);
        
        if (status === 'granted') {
          getCurrentLocation();
        } else {
          console.log('Location permission denied');
        }
      } catch (err) {
        console.error('Error getting location permission:', err);
      }
    };

    getLocationPermission();
  }, []);

  // Clear error when inputs change
  useEffect(() => {
    if (error) setError('');
    if (mapLoadFailed) setMapLoadFailed(false);
  }, [from, to]);

  // Get current location
  const getCurrentLocation = async () => {
    try {
      const { coords } = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });
      
      // Convert coordinates to location name using reverse geocoding
      const reverseGeocode = await Location.reverseGeocodeAsync({
        latitude: coords.latitude,
        longitude: coords.longitude,
      });
      
      // Save user location
      setUserLocation({
        coordinates: [coords.longitude, coords.latitude],
        placeName: reverseGeocode[0]?.name || 
                   `${reverseGeocode[0]?.district || ''} ${reverseGeocode[0]?.city || ''}`.trim() || 
                   'Current Location',
        address: reverseGeocode[0]
      });
      
      console.log('User location:', {
        coords: [coords.longitude, coords.latitude],
        address: reverseGeocode[0]
      });
    } catch (error) {
      console.error('Error getting current location:', error);
    }
  };

  // Set current location as From location
  const useCurrentLocationAsFrom = () => {
    if (userLocation) {
      setFrom(userLocation.placeName);
      setFromCoords(userLocation);
      setUsingCurrentLocation(true);
    } else {
      Alert.alert(
        "Location Not Available",
        "Could not access your current location. Please check your device settings."
      );
    }
  };

  // Refresh current location
  const refreshCurrentLocation = async () => {
    await getCurrentLocation();
    if (usingCurrentLocation) {
      setFrom(userLocation?.placeName || 'Current Location');
      setFromCoords(userLocation);
    }
  };

  const geocodePlace = async (place) => {
    try {
      console.log('Geocoding place:', place);

      // Check if using current location
      if (usingCurrentLocation && place === from && userLocation) {
        console.log('Using current location instead of geocoding');
        return userLocation;
      }

      // Enhanced place variations for Indian cities
      const placeVariations = {
        'nellore': 'Nellore, Andhra Pradesh, India',
        'rajam': 'Rajam, Srikakulam District, Andhra Pradesh, India',
        'vizag': 'Visakhapatnam, Andhra Pradesh, India',
        'visakhapatnam': 'Visakhapatnam, Andhra Pradesh, India',
        'vishakapatnam': 'Visakhapatnam, Andhra Pradesh, India',
        'hyderabad': 'Hyderabad, Telangana, India',
        'delhi': 'New Delhi, Delhi, India',
        'mumbai': 'Mumbai, Maharashtra, India',
        'bangalore': 'Bengaluru, Karnataka, India',
        'bengaluru': 'Bengaluru, Karnataka, India',
        'chennai': 'Chennai, Tamil Nadu, India',
        'kolkata': 'Kolkata, West Bengal, India',
        // Add more common variations as needed
      };

      // Clean and normalize the place name
      const normalizedPlace = place.toLowerCase().trim();
      const searchPlace = placeVariations[normalizedPlace] || `${normalizedPlace}, India`;

      console.log('Using search place:', searchPlace);

      // First attempt with specific place types
      let response = await geocodingClient
        .forwardGeocode({
          query: searchPlace,
          limit: 1,
          countries: ['IN'],
          types: ['place', 'district', 'locality', 'neighborhood', 'address'],
          language: ['en'],
          fuzzyMatch: true
        })
        .send();

      // If no results, try with broader search
      if (!response.body.features || response.body.features.length === 0) {
        console.log('No specific results, trying broader search');
        response = await geocodingClient
          .forwardGeocode({
            query: searchPlace,
            limit: 1,
            countries: ['IN'],
            types: ['place', 'locality', 'district', 'region', 'postcode', 'neighborhood', 'address', 'poi'],
            language: ['en'],
            fuzzyMatch: true
          })
          .send();
      }

      if (response.body.features && response.body.features.length > 0) {
        const feature = response.body.features[0];

        // Verify that we have valid coordinates
        if (!feature.center || feature.center.length !== 2 ||
            isNaN(feature.center[0]) || isNaN(feature.center[1])) {
          console.error('Invalid coordinates received:', feature.center);
          return null;
        }

        // Build a more detailed place name
        let placeName = feature.text || '';
        if (feature.context) {
          const district = feature.context.find(ctx => ctx.id.startsWith('district'));
          const state = feature.context.find(ctx => ctx.id.startsWith('region'));

          if (district) {
            placeName += placeName ? `, ${district.text}` : district.text;
          }
          if (state) {
            placeName += placeName ? `, ${state.text}` : state.text;
          }
        }

        // Ensure coordinates are in the correct format and valid
        const coordinates = [
          parseFloat(feature.center[0]),
          parseFloat(feature.center[1])
        ];

        console.log('Geocoding successful:', {
          originalPlace: place,
          normalizedPlace: searchPlace,
          coordinates: coordinates,
          placeName: placeName,
          fullName: feature.place_name
        });

        return {
          coordinates: coordinates,
          placeName: placeName || feature.place_name
        };
      }

      console.log('No geocoding results found for:', place);
      return null;
    } catch (error) {
      console.error('Geocoding error for place:', place, error);
      return null;
    }
  };

  // Simplified path function with configurable maximum points
  const simplifyPath = (coordinates, maxPoints = 50) => {
    if (!coordinates || coordinates.length <= maxPoints) {
      return coordinates;
    }

    const step = Math.ceil(coordinates.length / maxPoints);
    const simplified = [];

    // Always include first point
    simplified.push(coordinates[0]);

    // Sample points in between
    for (let i = step; i < coordinates.length - 1; i += step) {
      simplified.push(coordinates[i]);
    }

    // Always include last point
    simplified.push(coordinates[coordinates.length - 1]);

    console.log(`Simplified path from ${coordinates.length} to ${simplified.length} points`);
    return simplified;
  };

  // Generate map with route - improved version with fallbacks and higher zoom
  const generateMapWithRoute = (fromLocation, toLocation, route = null, zoom = 14) => {
    if (!fromLocation || !toLocation) {
      console.error("Missing location data for map generation");
      return;
    }

    if (!mapboxToken) {
      console.error("Mapbox token not available for map generation");
      setError('Mapbox API token is missing. Please check your configuration.');
      setMapLoadFailed(true);
      return;
    }

    // Validate coordinates
    if (!fromLocation.coordinates || fromLocation.coordinates.length !== 2 ||
        !toLocation.coordinates || toLocation.coordinates.length !== 2) {
      console.error("Invalid coordinates format");
      setError('Invalid location coordinates. Please try a different location.');
      setMapLoadFailed(true);
      return;
    }

    // Ensure coordinates are valid numbers
    const fromLon = parseFloat(fromLocation.coordinates[0]);
    const fromLat = parseFloat(fromLocation.coordinates[1]);
    const toLon = parseFloat(toLocation.coordinates[0]);
    const toLat = parseFloat(toLocation.coordinates[1]);

    if (isNaN(fromLon) || isNaN(fromLat) || isNaN(toLon) || isNaN(toLat)) {
      console.error("Coordinates contain NaN values");
      setError('Invalid location coordinates. Please try a different location.');
      setMapLoadFailed(true);
      return;
    }

    try {
      // Calculate appropriate image dimensions
      const imgWidth = Math.min(Math.floor(width * 0.7), 800);
      const imgHeight = Math.min(Math.floor(imgWidth * 0.8), 900);

      // Create markers for start and end points
      const startMarker = usingCurrentLocation ? 
        `pin-l-star+3FB1AF(${fromLon},${fromLat})` : 
        `pin-l-a+3FB1AF(${fromLon},${fromLat})`;
      const endMarker = `pin-l-b+FF4757(${toLon},${toLat})`;

      // Create a straight line between points in blue color
      const straightLine = `path-5+4287f5-0.8(${fromLon},${fromLat};${toLon},${toLat})`;

      // Calculate center point
      const centerLon = (fromLon + toLon) / 2;
      const centerLat = (fromLat + toLat) / 2;

      // Calculate distance and appropriate zoom level
      const distance = calculateDistance(fromLon, fromLat, toLon, toLat);
      let fixedZoom = 10; // Default zoom level

      // Adjust zoom based on distance
      if (distance < 2) fixedZoom = 13;
      else if (distance < 5) fixedZoom = 12;
      else if (distance < 15) fixedZoom = 11;
      else if (distance < 40) fixedZoom = 10;
      else if (distance < 100) fixedZoom = 9;
      else fixedZoom = 7;

      // Create the map URL with the straight blue line
      let mapUrl = `https://api.mapbox.com/styles/v1/mapbox/streets-v12/static/${straightLine},${startMarker},${endMarker}/${centerLon},${centerLat},${fixedZoom}/${imgWidth}x${imgHeight}?access_token=${mapboxToken}`;

      // If we have a route, add it to the map
      if (route?.geometry?.coordinates?.length > 0) {
        try {
          const coordinates = route.geometry.coordinates;
          const path = coordinates.map(coord => coord.join(',')).join(';');
          mapUrl = `https://api.mapbox.com/styles/v1/mapbox/streets-v12/static/${startMarker},${endMarker},path-5+4287f5-0.8(${encodeURIComponent(path)})/${centerLon},${centerLat},${fixedZoom}/${imgWidth}x${imgHeight}?access_token=${mapboxToken}`;
        } catch (pathError) {
          console.error('Error encoding path:', pathError);
          // Fall back to straight line map if path encoding fails
        }
      }

      // Validate URL length to prevent CloudFront errors
      if (mapUrl.length > 8192) {
        console.error('Map URL too long, falling back to markers-only map');
        mapUrl = `https://api.mapbox.com/styles/v1/mapbox/streets-v12/static/${straightLine},${startMarker},${endMarker}/${centerLon},${centerLat},${fixedZoom}/${imgWidth}x${imgHeight}?access_token=${mapboxToken}`;
      }

      console.log('Generated map URL:', mapUrl);
      setMapImageUrl(mapUrl);
      setMapLoadFailed(false);
    } catch (error) {
      console.error('Error generating map URL:', error);
      setMapLoadFailed(true);
      setError('Error generating map. Please try again.');
    }
  };

  const handleGetDirections = async () => {
    console.log('Getting directions from:', from, 'to:', to);
    if (!from || !to) {
      setError('Please enter both from and to locations');
      return;
    }

    if (!mapboxToken) {
      setError('Mapbox API token is not configured');
      return;
    }

    setLoading(true);
    setError('');
    setDirections(null);
    setMapImageUrl('');
    setRouteSteps([]);
    setMapLoadFailed(false);

    try {
      // Check if using current location for "from"
      let fromLocation;
      if (usingCurrentLocation && userLocation) {
        fromLocation = userLocation;
        console.log("Using current location as starting point:", fromLocation);
      } else {
        // Geocode with improved error handling
        fromLocation = await geocodePlace(from);
        if (!fromLocation) {
          setLoading(false);
          setError(`Could not find location: ${from}`);
          return;
        }
      }

      const toLocation = await geocodePlace(to);
      if (!toLocation) {
        setLoading(false);
        setError(`Could not find location: ${to}`);
        return;
      }

      setFromCoords(fromLocation);
      setToCoords(toLocation);

      console.log("From location:", JSON.stringify(fromLocation));
      console.log("To location:", JSON.stringify(toLocation));
      console.log("From coordinates:", fromLocation.coordinates);
      console.log("To coordinates:", toLocation.coordinates);

      // Calculate appropriate zoom level based on distance
      const zoom = calculateZoomLevel(fromLocation.coordinates, toLocation.coordinates);
      setMapZoom(zoom);

      // Generate a map with markers between locations (without route yet)
      generateMapWithRoute(fromLocation, toLocation, null, zoom);

      // Get directions with additional options
      const response = await directionsClient
        .getDirections({
          profile: 'driving',
          waypoints: [
            { coordinates: fromLocation.coordinates },
            { coordinates: toLocation.coordinates }
          ],
          geometries: 'geojson',
          steps: true,
          overview: 'full',
          annotations: ['distance', 'duration', 'speed'],
          language: 'en'
        })
        .send();

      if (response.body.routes && response.body.routes.length > 0) {
        const route = response.body.routes[0];
        setDirections(route);
        console.log("Route received:", route);

        // Update map with the actual route
        generateMapWithRoute(fromLocation, toLocation, route, zoom);

        // Extract and set route steps
        const steps = extractRouteSteps(route);
        setRouteSteps(steps);
      } else {
        setError('No route found between these locations');
      }
    } catch (error) {
      console.error('Error fetching directions:', error);
      setError('Error fetching directions. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Calculate distance between two coordinates using Haversine formula
  const calculateDistance = (lon1, lat1, lon2, lat2) => {
    // Radius of earth in km
    const R = 6371;

    // Convert degrees to radians
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const radLat1 = lat1 * Math.PI / 180;
    const radLat2 = lat2 * Math.PI / 180;

    // Haversine formula
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(radLat1) * Math.cos(radLat2) *
              Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c;

    return distance;
  };

  // Calculate appropriate zoom level based on the distance between points
  const calculateZoomLevel = (coord1, coord2) => {
    const lon1 = coord1[0];
    const lat1 = coord1[1];
    const lon2 = coord2[0];
    const lat2 = coord2[1];

    // Calculate distance using our helper function
    const distance = calculateDistance(lon1, lat1, lon2, lat2);

    // Determine zoom level based on distance - with increased zoom levels
    // These values are approximate and can be adjusted
    if (distance < 1) return 16; // Extremely close - maximum zoom
    if (distance < 2) return 15; // Very close
    if (distance < 5) return 14; // Close
    if (distance < 10) return 13; // Moderately close
    if (distance < 20) return 12; // Medium distance
    if (distance < 50) return 11; // Medium-far
    if (distance < 100) return 10; // Far
    if (distance < 200) return 9; // Very far
    if (distance < 500) return 8; // Extremely far
    if (distance < 1000) return 7; // Continental distance
    return 6; // Intercontinental distance
  };

  const handleSwapLocations = () => {
    // Reset the current location flag if swapping
    if (usingCurrentLocation) {
      setUsingCurrentLocation(false);
    }
    setFrom(to);
    setTo(from);
  };

  // Format duration from seconds to hours and minutes
  const formatDuration = (seconds) => {
    if (!seconds) return 'N/A';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (hours > 0) {
      return `${hours} hr ${minutes} min`;
    } else {
      return `${minutes} min`;
    }
  };

  // Extract route steps from the directions response
  const extractRouteSteps = (route) => {
    if (!route || !route.legs || route.legs.length === 0) {
      return [];
    }

    try {
      const steps = [];

      // Process each leg of the route
      route.legs.forEach(leg => {
        if (leg.steps && leg.steps.length > 0) {
          leg.steps.forEach(step => {
            if (step.maneuver && step.maneuver.instruction) {
              steps.push({
                instruction: step.maneuver.instruction,
                distance: (step.distance / 1000).toFixed(1),
                duration: step.duration
              });
            }
          });
        }
      });

      return steps;
    } catch (error) {
      console.error('Error extracting route steps:', error);
      return [];
    }
  };

  // Handle viewing full directions by toggling the showAllSteps state
  const handleViewFullDirections = () => {
    console.log('View full directions clicked');
    setShowAllSteps(!showAllSteps);
  };

  // Handle map refresh
  const handleRefreshMap = () => {
    if (fromCoords && toCoords) {
      generateMapWithRoute(fromCoords, toCoords, directions, mapZoom);
    }
  };

  // Increase zoom level for more detail
  const handleZoomIn = () => {
    const newZoom = Math.min(mapZoom + 1, 16); // Max zoom level 16
    setMapZoom(newZoom);
    if (fromCoords && toCoords) {
      generateMapWithRoute(fromCoords, toCoords, directions, newZoom);
    }
  };

  // Decrease zoom level to see more area
  const handleZoomOut = () => {
    const newZoom = Math.max(mapZoom - 1, 5); // Min zoom level 5
    setMapZoom(newZoom);
    if (fromCoords && toCoords) {
      generateMapWithRoute(fromCoords, toCoords, directions, newZoom);
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView style={styles.container} contentContainerStyle={styles.scrollContent}>
        <View style={styles.formContainer}>
          <Text style={styles.title}>Get Directions</Text>

          <View style={styles.inputContainer}>
            <View style={styles.locationIcon}>
              <Ionicons name="location" size={20} color={Colors.PRIMARY} />
            </View>
            <TextInput
              style={styles.input}
              placeholder="From location"
              value={from}
              onChangeText={(text) => {
                setFrom(text);
                // If user changes text, they're no longer using current location
                if (usingCurrentLocation) {
                  setUsingCurrentLocation(false);
                }
              }}
            />
            <TouchableOpacity 
              style={styles.locationButton}
              onPress={useCurrentLocationAsFrom}
            >
              <Ionicons 
                name="locate" 
                size={20} 
                color={usingCurrentLocation ? Colors.PRIMARY : Colors.GRAY} 
              />
            </TouchableOpacity>
          </View>

          <TouchableOpacity style={styles.swapButton} onPress={handleSwapLocations}>
            <Ionicons name="swap-vertical" size={20} color={Colors.PRIMARY} />
          </TouchableOpacity>

          <View style={styles.inputContainer}>
            <View style={styles.locationIcon}>
              <Ionicons name="location" size={20} color={Colors.PRIMARY} />
            </View>
            <TextInput
              style={styles.input}
              placeholder="To location"
              value={to}
              onChangeText={setTo}
            />
          </View>

          {error ? <Text style={styles.errorText}>{error}</Text> : null}

          <TouchableOpacity
            style={styles.button}
            onPress={handleGetDirections}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color={Colors.WHITE} />
            ) : (
              <Text style={styles.buttonText}>Get Directions</Text>
            )}
          </TouchableOpacity>
        </View>

        {directions && (
          <>
            <View style={styles.mapCard}>
              {/* Always show map content, don't depend on mapImageUrl for web platform */}
              {(Platform.OS === 'web' || (mapImageUrl && !mapLoadFailed)) ? (
                <>
                  {Platform.OS === 'web' ? (
                    // For web platform, use a simple iframe with Mapbox static image
                    <View style={styles.mapContainer}>
                      {mapImageUrl ? (
                        <iframe
                          src={mapImageUrl}
                          style={{
                            width: '100%',
                            height: '100%',
                            border: 'none',
                            borderRadius: '12px'
                          }}
                          title="Map"
                        />
                      ) : (
                        <View style={styles.mapPlaceholder}>
                          <Text>Map data not available</Text>
                        </View>
                      )}
                    </View>
                  ) : (
                    // For native platforms, show the static map image
                    <View style={styles.mapContainer}>
                      <Image
                        source={{ uri: mapImageUrl }}
                        style={styles.mapImage}
                        resizeMode="cover"
                      />
                      <View style={styles.mapControls}>
                        <TouchableOpacity style={styles.mapControlButton} onPress={handleZoomIn}>
                          <Ionicons name="add" size={24} color={Colors.PRIMARY} />
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.mapControlButton} onPress={handleZoomOut}>
                          <Ionicons name="remove" size={24} color={Colors.PRIMARY} />
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.mapControlButton} onPress={handleRefreshMap}>
                          <Ionicons name="refresh" size={24} color={Colors.PRIMARY} />
                        </TouchableOpacity>
                      </View>
                    </View>
                  )}
                </>
              ) : (
                // Show a placeholder if map failed to load
                <View style={styles.mapPlaceholder}>
                  <Text style={styles.placeholderText}>
                    {mapLoadFailed 
                      ? 'Map loading failed. Please try again.' 
                      : 'Loading map...'}
                  </Text>
                  {mapLoadFailed && (
                    <TouchableOpacity 
                      style={[styles.button, styles.refreshButton]} 
                      onPress={handleRefreshMap}
                    >
                      <Text style={styles.buttonText}>Retry</Text>
                    </TouchableOpacity>
                  )}
                </View>
              )}
            </View>

            {/* Route summary */}
            <View style={styles.summaryCard}>
              <Text style={styles.summaryTitle}>Route Summary</Text>
              
              {/* Route locations */}
              <View style={styles.routeLocations}>
                <View style={styles.locationRow}>
                  <View style={[styles.locationDot, styles.fromDot]} />
                  <Text style={styles.locationText} numberOfLines={1}>
                    {fromCoords?.placeName || from}
                    {usingCurrentLocation ? ' (Current Location)' : ''}
                  </Text>
                </View>
                
                <View style={styles.verticalLine} />
                
                <View style={styles.locationRow}>
                  <View style={[styles.locationDot, styles.toDot]} />
                  <Text style={styles.locationText} numberOfLines={1}>
                    {toCoords?.placeName || to}
                  </Text>
                </View>
              </View>
              
              {/* Distance and Duration */}
              <View style={styles.statsRow}>
                <View style={styles.statItem}>
                  <Ionicons name="speedometer-outline" size={20} color={Colors.PRIMARY} />
                  <Text style={styles.statValue}>
                    {directions.distance ? (directions.distance / 1000).toFixed(1) + ' km' : 'N/A'}
                  </Text>
                </View>
                
                <View style={styles.statItem}>
                  <Ionicons name="time-outline" size={20} color={Colors.PRIMARY} />
                  <Text style={styles.statValue}>
                    {formatDuration(directions.duration)}
                  </Text>
                </View>
              </View>
            </View>

            {/* Route steps - only showing if we have steps */}
            {routeSteps.length > 0 && (
              <View style={styles.stepsCard}>
                <Text style={styles.stepsTitle}>Directions</Text>
                
                {/* Show limited steps or all steps based on state */}
                {routeSteps.slice(0, showAllSteps ? routeSteps.length : 3).map((step, index) => (
                  <View key={index} style={styles.stepItem}>
                    <View style={styles.stepNumberContainer}>
                      <Text style={styles.stepNumber}>{index + 1}</Text>
                    </View>
                    <View style={styles.stepContent}>
                      <Text style={styles.stepInstruction}>{step.instruction}</Text>
                      <Text style={styles.stepDistance}>{step.distance} km</Text>
                    </View>
                  </View>
                ))}
                
                {/* Show/hide toggle button for directions */}
                {routeSteps.length > 3 && (
                  <TouchableOpacity
                    style={styles.viewAllButton}
                    onPress={handleViewFullDirections}
                  >
                    <Text style={styles.viewAllText}>
                      {showAllSteps ? 'Show Less' : `Show All (${routeSteps.length} steps)`}
                    </Text>
                    <Ionicons
                      name={showAllSteps ? 'chevron-up' : 'chevron-down'}
                      size={16}
                      color={Colors.PRIMARY}
                    />
                  </TouchableOpacity>
                )}
              </View>
            )}
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.WHITE,
  },
  container: {
    flex: 1,
    backgroundColor: Colors.BACKGROUND,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  formContainer: {
    backgroundColor: Colors.WHITE,
    borderRadius: 12,
    padding: 16,
    shadowColor: Colors.SHADOW,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    marginBottom: 16,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: Colors.TEXT,
    marginBottom: 16,
    textAlign: 'center',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.INPUT_BG,
    borderRadius: 8,
    marginBottom: 12,
    paddingHorizontal: 12,
    height: 50,
  },
  locationIcon: {
    marginRight: 8,
  },
  input: {
    flex: 1,
    height: 50,
    fontSize: 16,
    color: Colors.TEXT,
  },
  locationButton: {
    padding: 8,
  },
  swapButton: {
    alignSelf: 'center',
    backgroundColor: Colors.LIGHT_BG,
    padding: 10,
    borderRadius: 20,
    marginVertical: -6,
    zIndex: 10,
  },
  button: {
    backgroundColor: Colors.PRIMARY,
    borderRadius: 8,
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 8,
  },
  buttonText: {
    color: Colors.WHITE,
    fontSize: 16,
    fontWeight: 'bold',
  },
  refreshButton: {
    marginTop: 16,
    height: 40,
    alignSelf: 'center',
    paddingHorizontal: 24,
  },
  errorText: {
    color: Colors.ERROR,
    marginBottom: 10,
    textAlign: 'center',
  },
  mapCard: {
    backgroundColor: Colors.WHITE,
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
    shadowColor: Colors.SHADOW,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    width: '100%',
    aspectRatio: 1.2, // Adjusted for better proportions
    alignSelf: 'center', // Center the card
    maxWidth: 800, // Limit maximum width
  },
  mapContainer: {
    width: '100%',
    height: '100%',
    position: 'relative',
  },
  mapImage: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  mapPlaceholder: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.LIGHT_BG,
    borderRadius: 12,
  },
  placeholderText: {
    color: Colors.GRAY,
    fontSize: 16,
  },
  mapControls: {
    position: 'absolute',
    right: 10,
    top: 10,
    alignItems: 'center',
  },
  mapControlButton: {
    backgroundColor: Colors.WHITE,
    borderRadius: 20,
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    shadowColor: Colors.SHADOW,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 2,
  },
  summaryCard: {
    backgroundColor: Colors.WHITE,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: Colors.SHADOW,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.TEXT,
    marginBottom: 16,
  },
  routeLocations: {
    marginBottom: 16,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  locationDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  fromDot: {
    backgroundColor: Colors.PRIMARY,
  },
  toDot: {
    backgroundColor: Colors.SECONDARY,
  },
  verticalLine: {
    width: 2,
    height: 20,
    backgroundColor: Colors.GRAY_LIGHT,
    marginLeft: 5,
    marginVertical: 2,
  },
  locationText: {
    fontSize: 16,
    color: Colors.TEXT,
    flex: 1,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    borderTopColor: Colors.BORDER,
    paddingTop: 16,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT,
    marginLeft: 8,
  },
  stepsCard: {
    backgroundColor: Colors.WHITE,
    borderRadius: 12,
    padding: 16,
    shadowColor: Colors.SHADOW,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  stepsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.TEXT,
    marginBottom: 16,
  },
  stepItem: {
    flexDirection: 'row',
    marginBottom: 12,
    alignItems: 'flex-start',
  },
  stepNumberContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    marginTop: 2,
  },
  stepNumber: {
    color: Colors.WHITE,
    fontWeight: 'bold',
    fontSize: 14,
  },
  stepContent: {
    flex: 1,
  },
  stepInstruction: {
    fontSize: 16,
    color: Colors.TEXT,
    marginBottom: 4,
  },
  stepDistance: {
    fontSize: 14,
    color: Colors.GRAY,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
    marginTop: 8,
  },
  viewAllText: {
    color: Colors.PRIMARY,
    fontWeight: 'bold',
    marginRight: 4,
  },
});
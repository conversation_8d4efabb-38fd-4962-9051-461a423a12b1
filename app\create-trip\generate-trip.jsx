import { View, Text, Image, Dimensions, ActivityIndicator, Alert } from 'react-native';
import React, { useContext, useEffect, useState } from 'react';
import { Colors } from '../../constants/Colors';
import { CreateTripContext } from '../../Context/CreateTripContext';
import { useRouter } from 'expo-router';
import { AI_PROMPT } from './../../constants/Options';
import { chatSession } from './../../configs/AiModel';
import { auth, db } from './../../configs/FirebaseConfig';
import { setDoc, doc } from 'firebase/firestore';

const { width, height } = Dimensions.get('window');

export default function GenerateTrip() {
    const { tripData, setTripData } = useContext(CreateTripContext);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const router = useRouter();
    const user = auth.currentUser;

    useEffect(() => {
        generateAiTrip();
    }, []);

    const generateAiTrip = async () => {
        try {
            setLoading(true);
            setError(null);

            if (!tripData?.locationInfo?.name || !tripData?.totalNoOfDays) {
                const errorMsg = 'Missing required trip details. Please go back and fill in all required fields.';
                console.error(errorMsg, tripData);
                setError(errorMsg);
                setLoading(false);
                return;
            }

            if (!user) {
                const errorMsg = 'You need to be signed in to generate a trip.';
                console.error(errorMsg);
                setError(errorMsg);
                setLoading(false);
                return;
            }

            // Add distance information to the prompt if available
            let distanceInfo = '';
            try {
                if (tripData?.fromLocation?.name && tripData?.distance?.value) {
                    distanceInfo = `The trip starts from ${tripData.fromLocation.name}, which is approximately ${tripData.distance.value} ${tripData.distance.unit || 'km'} away from the destination.`;
                    console.log('Added distance info to prompt:', distanceInfo);
                }
            } catch (error) {
                console.error('Error adding distance info to prompt:', error);
            }

            const FINAL_PROMPT = AI_PROMPT
                .replace('{location}', tripData.locationInfo.name)
                .replace('{days}', tripData.totalNoOfDays)
                .replace('{distanceInfo}', distanceInfo);

            console.log('Generating trip with prompt:', FINAL_PROMPT);

            // Generate trip using AI
            const response = await chatSession.sendMessage(FINAL_PROMPT);
            console.log('Raw AI Response:', response);

            // Handle different response formats
            let generatedTrip;
            if (typeof response === 'string') {
                try {
                    generatedTrip = JSON.parse(response);
                } catch (e) {
                    console.error('Error parsing string response:', e);
                    throw new Error('Invalid response format from AI');
                }
            } else if (response && typeof response === 'object') {
                // If response has a text function, call it to get the content
                if (typeof response.text === 'function') {
                    const textContent = await response.text();
                    console.log('Text content from response:', textContent);
                    try {
                        generatedTrip = JSON.parse(textContent);
                    } catch (e) {
                        console.error('Error parsing response text:', e);
                        throw new Error('Invalid response format from AI');
                    }
                } else {
                    // If it's already an object, use it directly
                    generatedTrip = response;
                }
            } else {
                throw new Error('Unexpected response format from AI');
            }

            // Validate the generated trip
            if (!generatedTrip || typeof generatedTrip !== 'object') {
                throw new Error('Invalid trip plan format');
            }

            // Create a clean object without any functions
            const cleanTripData = {
                ...generatedTrip,
                userId: user.uid,
                createdAt: new Date().toISOString(),
                location: tripData.locationInfo.name,
                duration: tripData.totalNoOfDays,
                status: 'active'
            };

            // Remove any functions from the object
            Object.keys(cleanTripData).forEach(key => {
                if (typeof cleanTripData[key] === 'function') {
                    delete cleanTripData[key];
                }
            });

            console.log('Clean trip data to save:', cleanTripData);

            // Save trip to Firestore
            try {
                const tripRef = doc(db, 'trips', user.uid);
                await setDoc(tripRef, cleanTripData, { merge: true });

                console.log('Trip saved successfully');
                setTripData(prev => ({ ...prev, ...cleanTripData }));
                router.push('/trip-details');
            } catch (firestoreError) {
                console.error('Error saving trip to Firestore:', firestoreError);
                setError('Failed to save trip. Please try again.');
                Alert.alert(
                    'Error',
                    'Failed to save trip. Please try again.',
                    [{ text: 'OK', onPress: () => router.back() }]
                );
            }
        } catch (error) {
            console.error('Error generating trip:', error);
            setError('Failed to generate trip. Please try again.');
            Alert.alert(
                'Error',
                'Failed to generate trip. Please try again.',
                [{ text: 'OK', onPress: () => router.back() }]
            );
        } finally {
            setLoading(false);
        }
    };

    if (error) {
        return (
            <View style={styles.container}>
                <Text style={styles.errorText}>{error}</Text>
            </View>
        );
    }

    return (
        <View style={styles.container}>
            {loading ? (
                <ActivityIndicator size="large" color={Colors.PRIMARY} />
            ) : (
                <Text style={styles.text}>Generating your trip...</Text>
            )}
        </View>
    );
}

const styles = {
    container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: Colors.WHITE,
    },
    text: {
        fontSize: 18,
        color: Colors.TEXT,
        textAlign: 'center',
    },
    errorText: {
        fontSize: 16,
        color: Colors.ERROR,
        textAlign: 'center',
        padding: 20,
    },
};
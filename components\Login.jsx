import { View, Text, StyleSheet, Image, TouchableOpacity, Dimensions } from 'react-native';
import React from 'react';
import { Colors } from '@/constants/Colors';
import { useRouter } from 'expo-router';
import Ionicons from '@expo/vector-icons/Ionicons';

const { width, height } = Dimensions.get('window');

export default function Login() {
  const router = useRouter();

  return (
    <View style={styles.wrapper}>
      <Image 
        source={require('./../assets/images/icon.png')} 
        style={styles.image}
      />
      <View style={styles.container}>
        <Text style={styles.title}>AI Travel Planner</Text>
        <Text style={styles.description}>
          "Explore the world effortlessly with smart suggestions, dynamic itineraries, and real-time insights—turning every trip into an unforgettable adventure."
        </Text>
        <TouchableOpacity 
          style={styles.button} 
          onPress={() => router.push('auth/sign-in')}
          activeOpacity={0.8}
        >
          <Text style={styles.buttonText}>
            Get Start
          </Text>
          <Ionicons 
            name="arrow-forward" 
            size={Math.min(width * 0.06, 24)} 
            color={Colors.WHITE} 
            style={styles.buttonIcon}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    backgroundColor: Colors.WHITE,
  },
  image: {
    width: '100%',
    height: height * 0.55, 
    resizeMode: 'cover',
  },
  container: {
    backgroundColor: Colors.WHITE,
    marginTop: -height * 0.04, 
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    paddingHorizontal: width * 0.06,
    paddingVertical: height * 0.03,
    flex: 1,
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  title: {
    fontSize: 35,
    fontFamily: 'outfit-bold',
    textAlign: 'center',
    color: Colors.PRIMARY,
    marginBottom: height * 0.02,
  },
  description: {
    fontFamily: 'outfit-regular',
    fontSize: 19,
    textAlign: 'center',
    color: Colors.GRAY,
    paddingHorizontal: width * 0.04,
    lineHeight: Math.min(width * 0.06, 24),
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: height * 0.015,
    paddingHorizontal: width * 0.06,
    backgroundColor: Colors.PRIMARY,
    borderRadius: 50,
    marginTop: 40, 
    marginBottom: height * 0.04,
    width: width * 0.6,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
  },
  buttonText: {
    color: Colors.WHITE,
    fontFamily: 'outfit-medium',
    fontSize: Math.min(width * 0.045, 18),
    marginRight: width * 0.02,
  },
  buttonIcon: {
    marginLeft: width * 0.02,
  },
});
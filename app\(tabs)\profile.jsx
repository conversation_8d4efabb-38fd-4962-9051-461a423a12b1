import { View, Text, Image, TouchableOpacity, Alert } from 'react-native';
import React, { useEffect, useState } from 'react';
import { Colors } from '../../constants/Colors';
import { Feather, MaterialIcons, AntDesign } from '@expo/vector-icons';
import { getAuth, signOut, deleteUser, updatePassword, updateProfile } from 'firebase/auth';
import { useIsFocused } from '@react-navigation/native';
import * as ImagePicker from 'expo-image-picker';
import Toast from 'react-native-root-toast';

export default function Profile() {
  const auth = getAuth();

  const isFocused = useIsFocused();

  const [user, setUser] = useState(null);
  const [profilePic, setProfilePic] = useState(null);
  const [hasReviewed, setHasReviewed] = useState(false);

  
  useEffect(() => {
    if (auth.currentUser) {
      setUser(auth.currentUser);
      setProfilePic(auth.currentUser.photoURL);
    }
  }, [auth.currentUser]);

  useEffect(() => {
    if (!isFocused && hasReviewed) {
      Toast.show('Thanks for reviewing the app! 😊', {
        duration: Toast.durations.SHORT,
        position: Toast.positions.BOTTOM,
      });
      setHasReviewed(false);
    }
  }, [isFocused]);

  const handleEditProfilePic = async () => {
    const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    if (!permissionResult.granted) {
      Alert.alert('Permission Required', 'Please allow access to your photo gallery');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
    });

    if (!result.canceled) {
      try {
        await updateProfile(auth.currentUser, {
          photoURL: result.assets[0].uri
        });
        setProfilePic(result.assets[0].uri);
        Alert.alert('Success', 'Profile picture updated successfully');
      } catch (error) {
        Alert.alert('Error', 'Failed to update profile picture');
      }
    }
  };

  const handleLogout = async () => {
    try {
      await signOut(auth);
      navigation.navigate('auth/sign-in');
    } catch (error) {
      Alert.alert('Error', error.message);
    }
  };

  const handleDeleteAccount = async () => {
    Alert.alert(
      'Confirm Deletion',
      'Are you sure you want to delete your account? This action is irreversible!',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          onPress: async () => {
            try {
              if (auth.currentUser) {
                await deleteUser(auth.currentUser);
                Alert.alert('Success', 'Account successfully deleted.');
                navigation.navigate('Login');
              }
            } catch (error) {
              Alert.alert('Error', 'Please re-login before deleting your account.');
            }
          },
          style: 'destructive',
        },
      ]
    );
  };

  const handleChangePassword = async () => {
    Alert.prompt(
      'Change Password',
      'Enter your new password:',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Update',
          onPress: async (password) => {
            try {
              if (auth.currentUser && password) {
                await updatePassword(auth.currentUser, password);
                Alert.alert('Success', 'Password updated successfully');
              }
            } catch (error) {
              Alert.alert('Error', 'Failed to update password. Please re-login and try again.');
            }
          },
        },
      ],
      'secure-text'
    );
  };

  const handleReviewApp = () => {
    Alert.alert('Thanks!', 'Your feedback is valuable 💬');
    setHasReviewed(true);
  };

  const handleHelpInfo = () => {
    Alert.alert(
      'Help & Information',
      'For support, contact us at:\nEmail: <EMAIL>\nPhone: +91 9876543201\n\nApp Version: 1.0.0',
      [{ text: 'OK' }]
    );
  };

  return (
    <View>
      {/* Profile Section */}
      <View style={{ alignItems: 'center', marginTop: 50 }}>
        <Image
          source={profilePic ? { uri: profilePic } : require('./../../assets/images/icon.png')}
          style={{ width: 200, height: 200, borderRadius: 100 }}
        />
        <TouchableOpacity onPress={handleEditProfilePic}>
          <Text style={{ marginTop: 20, fontFamily: 'outfit', color: Colors.GRAY, fontSize: 15 }}>
            <Feather name="edit" size={15} /> Edit Profile Picture
          </Text>
        </TouchableOpacity>
        <Text style={{ fontFamily: 'outfit-medium', fontSize: 20, color: Colors.PRIMARY, marginTop: 15 }}>
          {user?.email || 'Guest User'}
        </Text>
      </View>

      {/* Account */}
      <View style={{ marginTop: 15, marginLeft: 10 }}>
        <Text style={{ fontFamily: 'outfit-bold', fontSize: 18, color: Colors.GRAY }}>Account</Text>
        <View style={{ marginLeft: 20, marginTop: 10 }}>
          <TouchableOpacity 
            onPress={handleLogout} 
            style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10 }}
          >
            <Feather name="log-out" size={18} color="black" />
            <Text style={{ fontFamily: 'outfit-medium', fontSize: 18, color: 'red', marginLeft: 10 }}>
              Logout
            </Text>
          </TouchableOpacity>

          <TouchableOpacity 
            onPress={handleChangePassword}
            style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10 }}
          >
            <MaterialIcons name="password" size={18} color="black" />
            <Text style={{ fontFamily: 'outfit-medium', fontSize: 18, color: Colors.PRIMARY, marginLeft: 10 }}>
              Change Password
            </Text>
          </TouchableOpacity>

          <TouchableOpacity 
            onPress={handleDeleteAccount} 
            style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10 }}
          >
            <AntDesign name="deleteuser" size={18} color="black" />
            <Text style={{ fontFamily: 'outfit-medium', fontSize: 18, color: 'red', marginLeft: 10 }}>
              Account Deletion
            </Text>
          </TouchableOpacity>

          <TouchableOpacity 
            onPress={handleReviewApp}
            style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10 }}
          >
            <Feather name="star" size={18} color="black" />
            <Text style={{ fontFamily: 'outfit-medium', fontSize: 18, color: Colors.PRIMARY, marginLeft: 10 }}>
              Review App
            </Text>
          </TouchableOpacity>

          <TouchableOpacity 
            onPress={handleHelpInfo}
            style={{ flexDirection: 'row', alignItems: 'center' }}
          >
            <Feather name="help-circle" size={18} color="black" />
            <Text style={{ fontFamily: 'outfit-medium', fontSize: 18, color: Colors.PRIMARY, marginLeft: 10 }}>
              Help & Info
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

import { View, Text, TouchableOpacity, ToastAndroid, Dimensions } from 'react-native';
import React, { useContext, useEffect, useState } from 'react';
import { useNavigation, useRouter } from 'expo-router';
import { Colors } from '../../constants/Colors';
import CalendarPicker from 'react-native-calendar-picker';
import moment from 'moment';
import { CreateTripContext } from '../../Context/CreateTripContext';

const { width, height } = Dimensions.get('window');

export default function SelectDates() {
    const navigation = useNavigation();
    const router = useRouter();
    const [startDate, setStartDate] = useState(null);
    const [endDate, setEndDate] = useState(null);
    const { tripData, setTripData } = useContext(CreateTripContext);

    useEffect(() => {
        navigation.setOptions({
            headerShown: true,
            headerTransparent: true,
            headerTitle: '',
            headerStyle: {
                borderBottomWidth: 0,
            },
        });
    }, []);

    const onDateChange = (date, type) => {
        const momentDate = moment(date);
        if (type === 'START_DATE') {
            setStartDate(momentDate);
            if (endDate && momentDate.isAfter(endDate)) {
                setEndDate(null);
            }
        } else if (type === 'END_DATE') {
            if (startDate && momentDate.isBefore(startDate)) {
                ToastAndroid.show('End date cannot be before start date', ToastAndroid.SHORT);
                return;
            }
            setEndDate(momentDate);
        }
    };

    const onDateSelectionContinue = () => {
        if (!startDate || !endDate) {
            ToastAndroid.show('Please select both Start and End Dates', ToastAndroid.LONG);
            return;
        }

        const totalNoOfDays = endDate.diff(startDate, 'days') + 1;
        if (totalNoOfDays <= 0) {
            ToastAndroid.show('Invalid date range selected', ToastAndroid.LONG);
            return;
        }

        setTripData({
            ...tripData,
            startDate: startDate,
            endDate: endDate,
            totalNoOfDays: totalNoOfDays
        });
        router.push('/create-trip/select-budget');
    };

    return (
        <View style={{
            flex: 1,
            backgroundColor: Colors.WHITE,
            paddingHorizontal: width * 0.06,
            paddingTop: height * 0.1,
        }}>
            <Text style={{
                fontFamily: 'outfit-bold',
                fontSize: Math.min(width * 0.09, 35),
                color: Colors.DARK,
                marginBottom: height * 0.03,
            }}>
                Travel Dates
            </Text>

            <View style={{
                flex: 1,
                marginBottom: height * 0.02,
            }}>
                <CalendarPicker
                    onDateChange={onDateChange}
                    allowRangeSelection={true}
                    minDate={new Date()}
                    maxRangeDuration={5}
                    startFromMonday={true}
                    todayBackgroundColor={Colors.LIGHT_GRAY}
                    selectedDayColor={Colors.PRIMARY}
                    selectedDayTextColor={Colors.WHITE}
                    scaleFactor={Math.min(width * 1.1, 375)} // Responsive scaling
                    textStyle={{
                        fontFamily: 'outfit-regular',
                        color: Colors.DARK,
                    }}
                    previousTitle="◄"
                    nextTitle="►"
                />

                {(startDate || endDate) && (
                    <View style={{
                        marginTop: height * 0.02,
                        padding: width * 0.03,
                        backgroundColor: Colors.LIGHT_GRAY,
                        borderRadius: 8,
                    }}>
                        <Text style={{ fontFamily: 'outfit-regular', color: Colors.DARK }}>
                            Start: {startDate ? startDate.format('MMM DD, YYYY') : 'Not selected'}
                        </Text>
                        <Text style={{ fontFamily: 'outfit-regular', color: Colors.DARK }}>
                            End: {endDate ? endDate.format('MMM DD, YYYY') : 'Not selected'}
                        </Text>
                        {startDate && endDate && (
                            <Text style={{ fontFamily: 'outfit-medium', color: Colors.PRIMARY }}>
                                Duration: {endDate.diff(startDate, 'days') + 1} days
                            </Text>
                        )}
                    </View>
                )}
            </View>

            <TouchableOpacity
                onPress={onDateSelectionContinue}
                style={{
                    position: 'absolute',
                    bottom: height * 0.03,
                    left: width * 0.06,
                    right: width * 0.06,
                    paddingVertical: height * 0.02,
                    backgroundColor: (startDate && endDate) ? Colors.PRIMARY : Colors.GRAY,
                    borderRadius: 15,
                    elevation: 5,
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 2 },
                    shadowOpacity: 0.2,
                    shadowRadius: 4,
                }}
                disabled={!startDate || !endDate}
            >
                <Text style={{
                    textAlign: 'center',
                    color: Colors.WHITE,
                    fontFamily: 'outfit-medium',
                    fontSize: Math.min(width * 0.05, 20),
                }}>
                    Continue
                </Text>
            </TouchableOpacity>
        </View>
    );
}
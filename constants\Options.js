export const SelectTravelerList = [
    {
      id: 1,
      title: 'Just Me',
      desc: 'A solo traveler exploring the world',
      icon: '🧳',
      people: '1 Person',
    },
    {
      id: 2,
      title: 'A Couple',
      desc: 'Two travelers in tandem',
      icon: '🥂',
      people: '2 People',
    },
    {
      id: 3,
      title: 'Family',
      desc: 'A fun-loving group of adventurers',
      icon: '🏡',
      people: '3 to 5 People',
    },
    {
      id: 4,
      title: 'Friends',
      desc: 'A bunch of thrill-seekers',
      icon: '🎉',
      people: '5 to 10 People',
    },
  ];
  
  export const SelectBudgetOptions = [
    {
      id: 1,
      title: 'Cheap',
      desc: 'Stay conscious of costs',
      icon: '💵',
    },
    {
      id: 2,
      title: 'Moderate',
      desc: 'Keep costs at an average level',
      icon: '💸',
    },
    {
      id: 3,
      title: 'Luxury',
      desc: "Don't worry about costs",
      icon: '💰',
    },
  ];

export const AI_PROMPT = `Generate a detailed travel plan for the location {location} for {totalDays} days and {totalNight} nights for a {traveler}, with a {budget} budget. The current date is {currentDate}, so all flight and hotel data must be from future dates only. The response should include: Flight information such as airline name, flight number, flight price, and a booking URL; Hotel details including name, address, price per night, image URL, geo coordinates (latitude and longitude), and rating; and A full itinerary broken down day-by-day for the total duration, including nearby places to visit for each day with place name, description, image URL, geo coordinates, ticket pricing, best time to visit, duration to spend on each activity, time of the day (morning, afternoon, or evening), and approximate travel time between places if applicable. Ensure that all information is relevant to {location}, the trip dates are after {currentDate}, and flight data must be strictly future-dated,JSON format only.`;

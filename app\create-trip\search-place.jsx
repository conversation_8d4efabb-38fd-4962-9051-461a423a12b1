import { View, Text, TextInput, FlatList, TouchableOpacity } from 'react-native';
import React, { useContext, useState, useCallback, useEffect } from 'react';
import { useNavigation, useRouter } from 'expo-router';
import { Colors } from '../../constants/Colors';
import { CreateTripContext } from './../../Context/CreateTripContext';
import debounce from 'lodash/debounce';


export default function SearchPlace() {
    const navigation = useNavigation();
    const { tripData, setTripData } = useContext(CreateTripContext);
    const router = useRouter();
    const [query, setQuery] = useState('');
    const [places, setPlaces] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);

    const MIN_SEARCH_LENGTH = 3;

    useEffect(() => {
        navigation.setOptions({
            headerShown: true,
            headerTransparent: true,
            headerTitle: '',
        });
    }, [navigation]);

    const searchPlaces = async (text) => {
        if (text.length >= MIN_SEARCH_LENGTH) {
            setIsLoading(true);
            setError(null);
            try {
                const response = await fetch(
                    `https://api.geoapify.com/v1/geocode/autocomplete?text=${encodeURIComponent(text)}&apiKey=${process.env.EXPO_PUBLIC_GEOAPIFY_API_KEY}`
                );
                if (!response.ok) throw new Error('Network response was not ok');
                const data = await response.json();
                setPlaces(data.features || []);
            } catch (error) {
                console.error('Error fetching places:', error);
                setError('Failed to load places. Please try again.');
            } finally {
                setIsLoading(false);
            }
        } else {
            setPlaces([]);
        }
    };

    const debouncedSearch = useCallback(
        debounce((text) => {
            searchPlaces(text);
        }, 300),
        []
    );

    const handleSearch = (text) => {
        setQuery(text);
        debouncedSearch(text);
    };

    const selectPlace = (place) => {
        setTripData({
            locationInfo: {
                name: place.properties.formatted,
                coordinates: {
                    lat: place.geometry.coordinates[1],
                    lon: place.geometry.coordinates[0],
                },
            },
        });
        router.push('/create-trip/select-traveler');
    };

    const clearSearch = () => {
        setQuery('');
        setPlaces([]);
        setError(null);
    };

    return (
        <View style={{
            padding: 25,
            paddingTop: 75,
            backgroundColor: Colors.GRAY,
            height: '100%',
        }}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <TextInput
                    style={{
                        flex: 1,
                        borderWidth: 1,
                        borderRadius: 5,
                        padding: 10,
                        backgroundColor: 'white',
                        marginRight: 10,
                    }}
                    placeholder="Search Place"
                    value={query}
                    onChangeText={handleSearch}
                />
                {query.length > 0 && (
                    <TouchableOpacity onPress={clearSearch}>
                        <Text style={{ color: Colors.PRIMARY }}>Cancel</Text>
                    </TouchableOpacity>
                )}
            </View>

            {isLoading && (
                <Text style={{
                    padding: 15,
                    borderBottomWidth: 1,
                    borderBottomColor: Colors.LIGHT_GRAY,
                    backgroundColor: 'white',
                    marginTop: 10,
                    borderRadius: 8,
                    elevation: 2, 
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 1 },
                    shadowOpacity: 0.1,
                    shadowRadius: 2,
                }}
                >
                    Loading...
                </Text>
            )}
            
            {error && (
                <Text style={{ 
                    marginTop: 10, 
                    color: 'red',
                    padding: 10,
                    backgroundColor: '#ffe6e6',
                    borderRadius: 5 
                }}>
                    {error}
                </Text>
            )}

            <FlatList
                data={places}
                keyExtractor={(item) => item.properties.place_id?.toString() || indexedDB.toString()}
                renderItem={({ item }) => (
                    <TouchableOpacity 
                        onPress={() => selectPlace(item)} 
                        style={{ 
                            padding: 15,
                            borderBottomWidth: 1,
                            borderBottomColor: Colors.LIGHT_GRAY,
                            backgroundColor: 'white',
                            marginTop: 10,
                        }}
                    >
                        <Text style={{ color: Colors.DARK }}>
                            {item.properties.formatted}
                        </Text>
                    </TouchableOpacity>
                )}
                ListEmptyComponent={!isLoading && query.length >= MIN_SEARCH_LENGTH && (
                    <Text style={{ 
                        padding: 15, 
                        textAlign: 'center',
                        color: Colors.DARK_GRAY 
                    }}>
                        No places found
                    </Text>
                )}
            />
        </View>
    );
}

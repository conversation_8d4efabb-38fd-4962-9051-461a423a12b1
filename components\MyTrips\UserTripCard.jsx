import { View, Text, Image, ActivityIndicator, TouchableOpacity } from 'react-native';
import React, { useEffect, useState } from 'react';
import moment from 'moment';
import { Colors } from './../../constants/Colors';
import { useRouter } from 'expo-router';

export default function UserTripCard({ trip }) {
  const [imageUrl, setImageUrl] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  // Parse tripData if it's a string; otherwise, use it directly
  const formatData = (data) => {
    try {
      if (typeof data === 'string') {
        return JSON.parse(data);
      }
      return data;
    } catch (error) {
      console.error('Error parsing data:', error);
      return null;
    }
  };

  const tripData = formatData(trip?.tripData);
  const location = tripData?.locationInfo?.name || 'Unknown Location'; // Use locationInfo.name from database
  const startDate = tripData?.startDate;
  const travelerTitle = tripData?.traveler?.title;

  // Format the start date
  let formattedStartDate;
  if (startDate) {
    if (startDate.toDate) { // Handle Firebase Timestamp
      formattedStartDate = moment(startDate.toDate()).format('DD MMM YYYY');
    } else {
      formattedStartDate = moment(startDate).format('DD MMM YYYY');
    }
  } else {
    formattedStartDate = moment(trip.createdAt).format('DD MMM YYYY'); // Fallback to createdAt
  }

  // Fetch image from Unsplash based on location
  useEffect(() => {
    const fetchUnsplashImage = async () => {
      if (!location || location === 'Unknown Location') return;
      setIsLoading(true);
      try {
        const response = await fetch(
          `https://api.unsplash.com/photos/random?query=${encodeURIComponent(location)}&client_id=${process.env.EXPO_PUBLIC_UNSPLASH_API_KEY}`
        );
        if (!response.ok) throw new Error('Unsplash API request failed');
        const data = await response.json();
        setImageUrl(data?.urls?.regular || null);
      } catch (error) {
        console.error('Error fetching Unsplash image:', error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchUnsplashImage();
  }, [location]);

  const handleSeePlan = () => {
    const tripString = JSON.stringify(trip);
    router.push({
      pathname: '/trip-details',
      params: { trip: tripString },
    });
  };

  return (
    <View
      style={{
        marginTop: 15,
        padding: 10,
        backgroundColor: '#fff',
        borderRadius: 15,
        flexDirection: 'row',
        gap: 15,
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 5,
        elevation: 3,
      }}
    >
      {/* Image Section */}
      <View style={{ position: 'relative' }}>
        {isLoading ? (
          <View
            style={{
              width: 100,
              height: 100,
              borderRadius: 15,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: Colors.LIGHT_GRAY,
            }}
          >
            <ActivityIndicator size="small" color={Colors.PRIMARY} />
          </View>
        ) : imageUrl ? (
          <Image
            source={{ uri: imageUrl }}
            style={{
              width: 100,
              height: 100,
              borderRadius: 15,
              resizeMode: 'cover',
            }}
          />
        ) : (
          <Image
            source={require('./../../assets/images/icon.png')}
            style={{
              width: 100,
              height: 100,
              borderRadius: 15,
              resizeMode: 'contain',
            }}
          />
        )}
      </View>

      {/* Details Section */}
      <View style={{ flex: 1 }}>
        <Text
          style={{
            fontFamily: 'outfit-medium',
            fontSize: 18,
            color: Colors.PRIMARY,
          }}
          numberOfLines={1}
        >
          {location}
        </Text>
        <Text style={{ fontFamily: 'outfit', fontSize: 14, color: Colors.GRAY, marginTop: 4 }}>
          {formattedStartDate}
        </Text>
        <Text style={{ fontFamily: 'outfit', fontSize: 14, color: Colors.GRAY, marginTop: 4 }}>
          Traveling: {travelerTitle || 'N/A'}
        </Text>
        <Text style={{ fontFamily: 'outfit', fontSize: 12, color: Colors.PRIMARY, marginTop: 4 }}>
          {tripData?.totalNoOfDays || 'N/A'} days trip
        </Text>

        {/* Action Button */}
        <View style={{ marginTop: 8 }}>
          <TouchableOpacity
            onPress={handleSeePlan}
            style={{
              backgroundColor: Colors.PRIMARY,
              paddingVertical: 8,
              paddingHorizontal: 12,
              borderRadius: 10,
              alignSelf: 'flex-start',
            }}
          >
            <Text style={{ color: Colors.WHITE, fontFamily: 'outfit-medium', fontSize: 12 }}>
              See Plan
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}
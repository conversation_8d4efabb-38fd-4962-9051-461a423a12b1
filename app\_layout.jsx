import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import { CreateTripContext } from '../Context/CreateTripContext'
import { useState } from "react";

export default function RootLayout() {
  const [tripData, setTripData] = useState({});
  useFonts({
    'outfit': require('./../assets/fonts/Outfit-Regular.ttf'),
    'outfit-bold': require('./../assets/fonts/Outfit-Bold.ttf'),
    'outfit-medium': require('./../assets/fonts/Outfit-Medium.ttf'),
  });

  return (
    <CreateTripContext.Provider value={{tripData,setTripData }}>
      <Stack
        screenOptions={{
          headerShown: false,
        }}
      >
        {/* <Stack.Screen name="index" options="false"*/}
        <Stack.Screen name="(tabs)" />
      </Stack>
    </CreateTripContext.Provider>
  );
}

import { View, Text, ActivityIndicator, ScrollView, Dimensions, TouchableOpacity } from 'react-native';
import React, { useEffect, useState } from 'react';
import { Colors } from './../../constants/Colors';
import Ionicons from '@expo/vector-icons/Ionicons';
import StartNewTripCard from './../../components/MyTrips/StartNewTripCard';
import UserTripList from '../../components/MyTrips/UserTripList';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { auth, db } from './../../configs/FirebaseConfig';
import { useRouter } from 'expo-router';

const { width, height } = Dimensions.get('window');

export default function MyTrip() {
    const [userTrips, setUserTrips] = useState([]);
    const [loading, setLoading] = useState(false);
    const user = auth.currentUser;
    const router = useRouter();

    useEffect(() => {
        if (user) {
            GetMyTrips();
        } else {
            console.log('No user authenticated');
            setLoading(false);
        }
    }, [user]);

    const GetMyTrips = async () => {
        if (!user) {
            setUserTrips([]);
            return;
        }

        setLoading(true);
        setUserTrips([]);

        try {
            const q = query(collection(db, 'UserTrips'), where('userEmail', '==', user.email));
            const querySnapshot = await getDocs(q);
            const trips = querySnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));

            // Sort trips by creation date (newest first) if createdAt exists
            if (trips.length > 0 && trips[0].createdAt) {
                trips.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
            }

            setUserTrips(trips);
        } catch (error) {
            console.error('Error fetching trips:', error.message);
        } finally {
            setLoading(false);
        }
    };

    const handleAddTrip = () => {
        router.push('/create-trip/search-place');
    };

    return (
        <View style={{
            flex: 1,
            backgroundColor: Colors.WHITE,
        }}>
            <View style={{
                paddingHorizontal: width * 0.06,
                paddingTop: height * 0.07,
                backgroundColor: Colors.WHITE,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                borderBottomWidth: 1,
                borderBottomColor: Colors.LIGHT_GRAY,
            }}>
                <Text style={{
                    fontFamily: 'outfit-bold',
                    fontSize: Math.min(width * 0.08, 32),
                    color: Colors.DARK,
                }}>
                    My Trips
                </Text>
                <TouchableOpacity onPress={handleAddTrip}>
                    <Ionicons 
                        name="add-circle" 
                        size={Math.min(width * 0.1, 40)} 
                        color={Colors.PRIMARY}
                    />
                </TouchableOpacity>
            </View>

            <ScrollView
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{
                    paddingHorizontal: width * 0.06,
                    paddingVertical: height * 0.02,
                    flexGrow: 1,
                }}
            >
                {loading ? (
                    <View style={{
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                        minHeight: height * 0.7,
                    }}>
                        <ActivityIndicator size="large" color={Colors.PRIMARY} />
                        <Text style={{
                            fontFamily: 'outfit-regular',
                            fontSize: Math.min(width * 0.04, 16),
                            color: Colors.GRAY,
                            marginTop: height * 0.02,
                        }}>
                            Loading your trips...
                        </Text>
                    </View>
                ) : userTrips.length === 0 ? (
                    <StartNewTripCard />
                ) : (
                    <UserTripList userTrips={userTrips} />
                )}
            </ScrollView>
        </View>
    );
}
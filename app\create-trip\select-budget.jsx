import { View, Text, TouchableOpacity, FlatList, Platform, ToastAndroid, Dimensions } from 'react-native';
import React, { useContext, useState, useEffect } from 'react';
import { useRouter, useNavigation } from 'expo-router';
import { Colors } from '../../constants/Colors';
import { SelectBudgetOptions } from '../../constants/Options';
import OptionCard from '../../components/CreateTrip/OptionCard';
import { CreateTripContext } from '../../Context/CreateTripContext';

const { width, height } = Dimensions.get('window');

export default function SelectBudget() {
    const navigation = useNavigation();
    const router = useRouter();
    const [selectedOption, setSelectedOption] = useState(null);
    const { tripData, setTripData } = useContext(CreateTripContext);

    useEffect(() => {
        navigation.setOptions({
            headerShown: true,
            headerTransparent: true,
            headerTitle: '',
            headerStyle: {
                borderBottomWidth: 0,
            },
        });
    }, [navigation]);

    useEffect(() => {
        if (selectedOption) {
            setTripData({
                ...tripData,
                budget: selectedOption.title,
            });
        }
    }, [selectedOption]);

    const showToast = (message) => {
        if (Platform.OS === 'android') {
            ToastAndroid.show(message, ToastAndroid.SHORT);
        } else {
            Alert.alert('Info', message);
        }
    };

    const onClickContinue = () => {
        if (!selectedOption) {
            showToast('Please select a budget option');
            return;
        }
        router.push('/create-trip/select-from');
    };

    return (
        <View style={{
            flex: 1,
            backgroundColor: Colors.WHITE,
            paddingHorizontal: width * 0.06,
            paddingTop: height * 0.1,
        }}>
            <Text style={{
                fontFamily: 'outfit-bold',
                fontSize: Math.min(width * 0.09, 35),
                color: Colors.DARK,
                marginBottom: height * 0.03,
            }}>
                Budget
            </Text>

            <View style={{
                flex: 1,
                marginBottom: height * 0.02,
            }}>
                <Text style={{
                    fontFamily: 'outfit-medium',
                    fontSize: Math.min(width * 0.05, 20),
                    color: Colors.GRAY,
                    marginBottom: height * 0.025,
                }}>
                    Choose your spending preference
                </Text>

                <FlatList
                    data={SelectBudgetOptions}
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={{
                        paddingBottom: height * 0.15,
                    }}
                    renderItem={({ item }) => (
                        <TouchableOpacity
                            onPress={() => setSelectedOption(item)}
                            style={{
                                marginVertical: height * 0.015,
                            }}
                        >
                            <OptionCard
                                option={item}
                                selectedOption={selectedOption}
                                style={{
                                    padding: width * 0.04,
                                    borderRadius: 12,
                                    backgroundColor: selectedOption?.id === item.id
                                        ? Colors.PRIMARY + '20'
                                        : Colors.WHITE,
                                    borderWidth: 1,
                                    borderColor: selectedOption?.id === item.id
                                        ? Colors.PRIMARY
                                        : Colors.LIGHT_GRAY,
                                    elevation: 2,
                                    shadowColor: '#000',
                                    shadowOffset: { width: 0, height: 2 },
                                    shadowOpacity: 0.1,
                                    shadowRadius: 4,
                                }}
                            />
                        </TouchableOpacity>
                    )}
                    keyExtractor={(item) => item.id.toString()}
                    ListEmptyComponent={() => (
                        <Text style={{
                            textAlign: 'center',
                            color: Colors.GRAY,
                            fontFamily: 'outfit-regular',
                            fontSize: width * 0.04,
                            marginTop: height * 0.05,
                        }}>
                            No budget options available
                        </Text>
                    )}
                />
            </View>

            <TouchableOpacity
                onPress={onClickContinue}
                style={{
                    position: 'absolute',
                    bottom: height * 0.03,
                    left: width * 0.06,
                    right: width * 0.06,
                    paddingVertical: height * 0.02,
                    backgroundColor: selectedOption ? Colors.PRIMARY : Colors.GRAY,
                    borderRadius: 15,
                    elevation: 5,
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 2 },
                    shadowOpacity: 0.2,
                    shadowRadius: 4,
                }}
                disabled={!selectedOption}
            >
                <Text style={{
                    textAlign: 'center',
                    color: Colors.WHITE,
                    fontFamily: 'outfit-medium',
                    fontSize: Math.min(width * 0.05, 20),
                }}>
                    Continue
                </Text>
            </TouchableOpacity>
        </View>
    );
}
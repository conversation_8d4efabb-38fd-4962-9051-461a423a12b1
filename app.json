{"expo": {"name": "AItravelplannerapp", "slug": "AItravelplannerapp", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "infoPlist": {"NSPhotoLibraryUsageDescription": "Allow access to your photo library to update your profile picture", "NSLocationWhenInUseUsageDescription": "This app needs access to location when open to show directions on the map.", "NSLocationAlwaysUsageDescription": "This app needs access to location when in the background to show directions on the map."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "permissions": ["READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["@react-native-mapbox-gl/maps", {"RNMapboxMapsDownloadToken": "YOUR_MAPBOX_DOWNLOAD_TOKEN"}]], "experiments": {"typedRoutes": true}}}
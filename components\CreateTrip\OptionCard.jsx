import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import React from 'react';
import { Colors } from '../../constants/Colors';

export default function OptionCard({ option, selectedOption }) {
  const isSelected = selectedOption?.id === option?.id;

  return (
    <View style={[
      styles.container,
      isSelected && styles.selectedContainer,
    ]}>
      <View style={styles.content}>
        <Text style={styles.title}>
          {option?.title || 'No Title'}
        </Text>
        <Text style={styles.description}>
          {option?.desc || 'No description available'}
        </Text>
      </View>
      <Text style={styles.icon}>
        {option?.icon || '📍'}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.LIGHT_GRAY,
    borderRadius: 15,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedContainer: {
    borderWidth: 3,
    borderColor: Colors.PRIMARY,
    backgroundColor: Colors.WHITE,
  },
  content: {
    flex: 1,
    marginRight: 15,
  },
  title: {
    fontSize: 20,
    fontFamily: 'outfit-bold',
    color: Colors.DARK,
    marginBottom: 5,
  },
  description: {
    fontSize: 16,
    fontFamily: 'outfit',
    color: Colors.GRAY,
    lineHeight: 22,
  },
  icon: {
    fontSize: 30,
    paddingLeft: 10,
  },
});
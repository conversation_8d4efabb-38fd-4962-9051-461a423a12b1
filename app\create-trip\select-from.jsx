import { View, Text, TextInput, FlatList, TouchableOpacity, StyleSheet, ActivityIndicator, Dimensions, Alert } from 'react-native';
import React, { useContext, useState, useCallback, useEffect } from 'react';
import { useNavigation, useRouter } from 'expo-router';
import { Colors } from '../../constants/Colors';
import { CreateTripContext } from '../../Context/CreateTripContext';
import debounce from 'lodash/debounce';
import * as Location from 'expo-location';
import { Ionicons } from '@expo/vector-icons';

const { width, height } = Dimensions.get('window');

export default function SelectFrom() {
    const navigation = useNavigation();
    const { tripData, setTripData } = useContext(CreateTripContext);
    const router = useRouter();
    const [query, setQuery] = useState('');
    const [places, setPlaces] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [userLocation, setUserLocation] = useState(null);
    const [locationPermission, setLocationPermission] = useState(null);
    const [loadingLocation, setLoadingLocation] = useState(false);

    const MIN_SEARCH_LENGTH = 3;

    useEffect(() => {
        navigation.setOptions({
            headerShown: true,
            headerTransparent: true,
            headerTitle: '',
        });

        // Request location permission when component mounts
        getLocationPermission();
    }, [navigation]);

    // Request location permission and get user's current location
    const getLocationPermission = async () => {
        try {
            const { status } = await Location.requestForegroundPermissionsAsync();
            setLocationPermission(status);

            if (status === 'granted') {
                getCurrentLocation();
            } else {
                console.log('Location permission denied');
            }
        } catch (err) {
            console.error('Error getting location permission:', err);
        }
    };

    // Get current location
    const getCurrentLocation = async () => {
        try {
            setLoadingLocation(true);
            const { coords } = await Location.getCurrentPositionAsync({
                accuracy: Location.Accuracy.Balanced,
            });

            // Convert coordinates to location name using reverse geocoding
            const reverseGeocode = await Location.reverseGeocodeAsync({
                latitude: coords.latitude,
                longitude: coords.longitude,
            });

            // Format the location name
            const locationName = reverseGeocode[0]?.name ||
                               `${reverseGeocode[0]?.district || ''} ${reverseGeocode[0]?.city || ''}`.trim() ||
                               'Current Location';

            // Save user location
            setUserLocation({
                coordinates: [coords.longitude, coords.latitude],
                placeName: locationName,
                address: reverseGeocode[0]
            });

            console.log('User location:', {
                coords: [coords.longitude, coords.latitude],
                address: reverseGeocode[0]
            });
        } catch (error) {
            console.error('Error getting current location:', error);
            setError('Could not access your current location. Please check your device settings.');
        } finally {
            setLoadingLocation(false);
        }
    };

    const searchPlaces = async (text) => {
        if (text.length >= MIN_SEARCH_LENGTH) {
            setIsLoading(true);
            setError(null);
            try {
                const response = await fetch(
                    `https://api.geoapify.com/v1/geocode/autocomplete?text=${encodeURIComponent(text)}&apiKey=${process.env.EXPO_PUBLIC_GEOAPIFY_API_KEY}`
                );
                if (!response.ok) throw new Error('Network response was not ok');
                const data = await response.json();
                setPlaces(data.features || []);
            } catch (error) {
                console.error('Error fetching places:', error);
                setError('Failed to load places. Please try again.');
            } finally {
                setIsLoading(false);
            }
        } else {
            setPlaces([]);
        }
    };

    const debouncedSearch = useCallback(
        debounce((text) => {
            searchPlaces(text);
        }, 300),
        []
    );

    const handleSearch = (text) => {
        setQuery(text);
        debouncedSearch(text);
    };

    const selectPlace = (place) => {
        try {
            console.log('Selected place:', place.properties.formatted);

            // Create a new fromLocation object
            const fromLocationObj = {
                name: place.properties.formatted,
                coordinates: {
                    lat: place.geometry.coordinates[1],
                    lon: place.geometry.coordinates[0],
                },
            };

            // First update the trip data with the from location
            setTripData(prevData => {
                const newData = {
                    ...prevData,
                    fromLocation: fromLocationObj
                };
                console.log('Updated trip data with from location:', newData);
                return newData;
            });

            // Calculate distance if destination is already selected
            if (tripData?.locationInfo?.coordinates?.lat && tripData?.locationInfo?.coordinates?.lon) {
                console.log('Calculating distance to destination:', tripData.locationInfo);
                calculateDistance(
                    place.geometry.coordinates[1],
                    place.geometry.coordinates[0],
                    tripData.locationInfo.coordinates.lat,
                    tripData.locationInfo.coordinates.lon
                );
            } else {
                console.log('Destination coordinates not available for distance calculation');
            }

            // Navigate to review trip page
            setTimeout(() => {
                router.push('/create-trip/review-trip');
            }, 300); // Small delay to ensure state updates complete
        } catch (error) {
            console.error('Error selecting place:', error);
            Alert.alert(
                "Error",
                "There was a problem selecting this location. Please try again or choose another location."
            );
        }
    };

    const useCurrentLocation = () => {
        try {
            if (!userLocation) {
                Alert.alert(
                    "Location Not Available",
                    "Could not access your current location. Please check your device settings."
                );
                return;
            }

            console.log('Using current location:', userLocation);

            // Create a new fromLocation object
            const fromLocationObj = {
                name: userLocation.placeName,
                coordinates: {
                    lat: userLocation.coordinates[1],
                    lon: userLocation.coordinates[0],
                },
                isCurrentLocation: true
            };

            // First update the trip data with the from location
            setTripData(prevData => {
                const newData = {
                    ...prevData,
                    fromLocation: fromLocationObj
                };
                console.log('Updated trip data with from location:', newData);
                return newData;
            });

            // Calculate distance if destination is already selected
            if (tripData?.locationInfo?.coordinates?.lat && tripData?.locationInfo?.coordinates?.lon) {
                console.log('Calculating distance to destination:', tripData.locationInfo);
                calculateDistance(
                    userLocation.coordinates[1],
                    userLocation.coordinates[0],
                    tripData.locationInfo.coordinates.lat,
                    tripData.locationInfo.coordinates.lon
                );
            } else {
                console.log('Destination coordinates not available for distance calculation');
            }

            // Navigate to review trip page
            setTimeout(() => {
                router.push('/create-trip/review-trip');
            }, 300); // Small delay to ensure state updates complete
        } catch (error) {
            console.error('Error using current location:', error);
            Alert.alert(
                "Error",
                "There was a problem using your current location. Please try again or enter a location manually."
            );
        }
    };

    // Calculate distance between two coordinates using Haversine formula
    const calculateDistance = (lat1, lon1, lat2, lon2) => {
        try {
            // Radius of earth in km
            const R = 6371;

            // Convert degrees to radians
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLon = (lon2 - lon1) * Math.PI / 180;
            const radLat1 = lat1 * Math.PI / 180;
            const radLat2 = lat2 * Math.PI / 180;

            // Haversine formula
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                    Math.cos(radLat1) * Math.cos(radLat2) *
                    Math.sin(dLon/2) * Math.sin(dLon/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            const distance = R * c;

            console.log(`Distance calculated: ${distance.toFixed(2)} km`);

            // Save the distance to the trip data using a more reliable approach
            // that doesn't trigger Firestore operations
            const distanceObj = {
                value: distance.toFixed(2),
                unit: 'km'
            };

            setTripData(prevData => {
                const newData = {
                    ...prevData,
                    distance: distanceObj
                };
                console.log('Updated trip data with distance:', newData);
                return newData;
            });

            return distance;
        } catch (error) {
            console.error('Error calculating distance:', error);
            return 0;
        }
    };

    const clearSearch = () => {
        setQuery('');
        setPlaces([]);
        setError(null);
    };

    return (
        <View style={styles.container}>
            <Text style={styles.title}>Starting Point</Text>

            <Text style={styles.subtitle}>
                Where will you be traveling from?
            </Text>

            {/* Current Location Button */}
            <TouchableOpacity
                style={styles.currentLocationButton}
                onPress={useCurrentLocation}
                disabled={loadingLocation || !userLocation}
            >
                {loadingLocation ? (
                    <ActivityIndicator size="small" color={Colors.WHITE} />
                ) : (
                    <>
                        <Ionicons name="locate" size={20} color={Colors.WHITE} style={styles.locationIcon} />
                        <Text style={styles.currentLocationText}>
                            Use Current Location
                        </Text>
                    </>
                )}
            </TouchableOpacity>

            <Text style={styles.orText}>OR</Text>

            {/* Search Input */}
            <View style={styles.searchContainer}>
                <TextInput
                    style={styles.searchInput}
                    placeholder="Search for a location"
                    value={query}
                    onChangeText={handleSearch}
                />
                {query.length > 0 && (
                    <TouchableOpacity onPress={clearSearch} style={styles.clearButton}>
                        <Ionicons name="close-circle" size={20} color={Colors.GRAY} />
                    </TouchableOpacity>
                )}
            </View>

            {isLoading && (
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="small" color={Colors.PRIMARY} />
                    <Text style={styles.loadingText}>Searching locations...</Text>
                </View>
            )}

            {error && (
                <Text style={styles.errorText}>{error}</Text>
            )}

            <FlatList
                data={places}
                keyExtractor={(item) => item.properties.place_id?.toString() || Math.random().toString()}
                renderItem={({ item }) => (
                    <TouchableOpacity
                        onPress={() => selectPlace(item)}
                        style={styles.placeItem}
                    >
                        <Ionicons name="location-outline" size={20} color={Colors.PRIMARY} style={styles.placeIcon} />
                        <Text style={styles.placeText}>
                            {item.properties.formatted}
                        </Text>
                    </TouchableOpacity>
                )}
                ListEmptyComponent={!isLoading && query.length >= MIN_SEARCH_LENGTH && (
                    <Text style={styles.emptyText}>
                        No places found
                    </Text>
                )}
                style={styles.placesList}
            />

            <TouchableOpacity
                style={styles.skipButton}
                onPress={() => router.push('/create-trip/review-trip')}
            >
                <Text style={styles.skipText}>Skip for now</Text>
            </TouchableOpacity>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.WHITE,
        paddingHorizontal: width * 0.06,
        paddingTop: height * 0.1,
    },
    title: {
        fontFamily: 'outfit-bold',
        fontSize: Math.min(width * 0.09, 35),
        color: Colors.DARK,
        marginBottom: height * 0.02,
    },
    subtitle: {
        fontFamily: 'outfit-medium',
        fontSize: Math.min(width * 0.045, 18),
        color: Colors.GRAY,
        marginBottom: height * 0.03,
    },
    currentLocationButton: {
        backgroundColor: Colors.PRIMARY,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: height * 0.018,
        borderRadius: 12,
        marginBottom: height * 0.02,
    },
    currentLocationText: {
        color: Colors.WHITE,
        fontFamily: 'outfit-medium',
        fontSize: Math.min(width * 0.045, 18),
    },
    locationIcon: {
        marginRight: width * 0.02,
    },
    orText: {
        textAlign: 'center',
        fontFamily: 'outfit-medium',
        fontSize: Math.min(width * 0.04, 16),
        color: Colors.GRAY,
        marginVertical: height * 0.015,
    },
    searchContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: Colors.LIGHT_GRAY,
        borderRadius: 12,
        paddingHorizontal: width * 0.04,
        marginBottom: height * 0.02,
    },
    searchInput: {
        flex: 1,
        fontFamily: 'outfit',
        fontSize: Math.min(width * 0.045, 18),
        paddingVertical: height * 0.015,
    },
    clearButton: {
        padding: 5,
    },
    loadingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: height * 0.02,
    },
    loadingText: {
        fontFamily: 'outfit',
        fontSize: Math.min(width * 0.04, 16),
        color: Colors.GRAY,
        marginLeft: width * 0.02,
    },
    errorText: {
        fontFamily: 'outfit',
        fontSize: Math.min(width * 0.04, 16),
        color: 'red',
        marginBottom: height * 0.02,
    },
    placesList: {
        flex: 1,
    },
    placeItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: height * 0.018,
        borderBottomWidth: 1,
        borderBottomColor: Colors.LIGHT_GRAY,
    },
    placeIcon: {
        marginRight: width * 0.03,
    },
    placeText: {
        fontFamily: 'outfit',
        fontSize: Math.min(width * 0.04, 16),
        color: Colors.DARK,
        flex: 1,
    },
    emptyText: {
        fontFamily: 'outfit',
        fontSize: Math.min(width * 0.04, 16),
        color: Colors.GRAY,
        textAlign: 'center',
        marginTop: height * 0.03,
    },
    skipButton: {
        alignSelf: 'center',
        paddingVertical: height * 0.015,
        marginBottom: height * 0.03,
    },
    skipText: {
        fontFamily: 'outfit-medium',
        fontSize: Math.min(width * 0.04, 16),
        color: Colors.GRAY,
    },
});

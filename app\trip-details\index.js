
import { ScrollView, View, Text, Image, Dimensions, ActivityIndicator } from 'react-native';
import React, { useEffect, useState } from 'react';
import { useLocalSearchParams, useNavigation } from 'expo-router';
import { Colors } from './../../constants/Colors';
import moment from 'moment';
import FlightInfo from './../../components/TripDetails/FlightInfo';
import HotelList from './../../components/TripDetails/HotelList';
import PlannedTrip from './../../components/TripDetails/PlannedTrip';

const { width, height } = Dimensions.get('window');

export default function TripDetails() {
  const navigation = useNavigation();
  const { trip } = useLocalSearchParams();
  const [tripDetails, setTripDetails] = useState(null);
  const [imageUrl, setImageUrl] = useState(null);
  const [loadingImage, setLoadingImage] = useState(false);

  // Simplified parsing function
  const formatData = (data) => {
    try {
      if (typeof data === 'string') {
        return JSON.parse(data);
      }
      return data;
    } catch (error) {
      console.error('Error parsing trip data:', error, 'Raw data:', data);
      return null;
    }
  };

  useEffect(() => {
    navigation.setOptions({
      headerShown: true,
      headerTransparent: true,
      headerTitle: '',
      headerStyle: { borderBottomWidth: 0 },
    });

    if (trip) {
      console.log('Received Trip Param:', trip);
      const parsedTrip = formatData(trip);
      if (parsedTrip) {
        console.log('Successfully parsed Trip Details:', parsedTrip);
        setTripDetails(parsedTrip);
        fetchUnsplashImage(parsedTrip?.tripData?.locationInfo?.name);
      } else {
        console.log('Failed to parse trip data, using fallback');
        setTripDetails({ tripData: {}, tripPlan: { trip: { flights: [], hotel: {}, itinerary: [] } } });
      }
    }
  }, [trip]);

  const fetchUnsplashImage = async (query) => {
    if (!query) return;
    setLoadingImage(true);
    try {
      const response = await fetch(
        `https://api.unsplash.com/photos/random?query=${encodeURIComponent(query)}&client_id=${process.env.EXPO_PUBLIC_UNSPLASH_API_KEY}`
      );
      if (!response.ok) throw new Error('Failed to fetch image');
      const data = await response.json();
      setImageUrl(data.urls?.regular || null);
    } catch (error) {
      console.error('Error fetching Unsplash image:', error);
      setImageUrl(null);
    } finally {
      setLoadingImage(false);
    }
  };

  if (!tripDetails) {
    return (
      <View style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: Colors.WHITE,
      }}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={{ fontFamily: 'outfit-medium', fontSize: 18, color: Colors.GRAY, marginTop: 10 }}>
          Loading trip details...
        </Text>
      </View>
    );
  }

  const tripData = tripDetails.tripData || {};
  const tripPlan = tripDetails.tripPlan || {};

  const formatDate = (date) => {
    if (!date) return 'N/A';
    const parsedDate = date.seconds ? new Date(date.seconds * 1000) : new Date(date);
    return moment(parsedDate).isValid() ? moment(parsedDate).format('DD MMM YYYY') : 'N/A';
  };


  const startDate = formatDate(tripData.startDate);
  const endDate = formatDate(tripData.endDate);

  console.log('Trip Data for UI:', tripData);
  console.log('Trip Plan for UI:', tripPlan);

  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      style={{ backgroundColor: Colors.LIGHT_GRAY }}
    >
      {loadingImage ? (
        <View style={{
          width: '100%',
          height: height * 0.4,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: Colors.LIGHT_GRAY,
        }}>
          <ActivityIndicator size="large" color={Colors.PRIMARY} />
        </View>
      ) : (
        <Image
          source={imageUrl ? { uri: imageUrl } : require('./../../assets/images/icon.png')}
          style={{
            width: '100%',
            height: height * 0.4,
            resizeMode: 'cover',
          }}
        />
      )}

      <View style={{
        paddingHorizontal: 20,
        paddingVertical: 20,
        backgroundColor: Colors.WHITE,
        marginTop: -30,
        borderTopLeftRadius: 30,
        borderTopRightRadius: 30,
        minHeight: height * 0.6,
      }}>
        <Text style={{
          fontFamily: 'outfit-bold',
          fontSize: 26,
          color: Colors.DARK,
        }}>
          {tripData?.locationInfo?.name || 'Unknown Destination'}
        </Text>

        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginTop: 10,
          marginBottom: 15,
        }}>
          <Text style={{
            fontFamily: 'outfit',
            fontSize: 16,
            color: Colors.GRAY,
          }}>
            {startDate} - {endDate}
          </Text>
          <Text style={{
            fontFamily: 'outfit-medium',
            fontSize: 16,
            color: Colors.PRIMARY,
            marginLeft: 10,
          }}>
            ({tripData?.totalNoOfDays || 'N/A'} Days)
          </Text>
        </View>

        <Text style={{
          fontFamily: 'outfit',
          fontSize: 18,
          color: Colors.GRAY,
        }}>
          {`${tripData?.traveler?.icon || ''} ${tripData?.traveler?.title || 'Traveler not specified'}`}
        </Text>

        <Text style={{
          fontFamily: 'outfit',
          fontSize: 18,
          color: Colors.GRAY,
          marginTop: 10,
        }}>
          💰 Budget: {tripData?.budget || 'N/A'}
        </Text>

        <FlightInfo flightData={tripPlan?.trip?.flights || []} />
        <HotelList hotelData={tripPlan?.trip?.hotel || {}} />
        <PlannedTrip details={tripPlan?.trip?.itinerary || []} />
      </View>
    </ScrollView>
  );
}
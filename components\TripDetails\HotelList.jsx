
import { View, Text, Image, ActivityIndicator, TouchableOpacity, StyleSheet } from 'react-native';
import React, { useEffect, useState } from 'react';
import { Colors } from '../../constants/Colors';
import * as Linking from 'expo-linking';

export default function HotelList({ hotelData }) {
  const [loadingImage, setLoadingImage] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Use the provided fallback image path relative to the project root
  const fallbackImage = require('./../../assets/images/hotel.jpg');

  // Assuming hotelData is tripPlan.trip.hotel from the database (single object)
  const hotel = hotelData || {};

  const openBookingLink = (hotelName) => {
    const searchQuery = encodeURIComponent(`${hotelName} hotel booking Shimla`);
    Linking.openURL(`https://www.google.com/search?q=${searchQuery}`).catch(err =>
      console.error('Failed to open URL:', err)
    );
  };

  // Preload image for the single hotel
  useEffect(() => {
    if (hotel.image_url && hotel.image_url.startsWith('http')) {
      setLoadingImage(true);
      Image.prefetch(hotel.image_url)
        .then(() => {
          setLoadingImage(false);
        })
        .catch(() => {
          setImageError(true);
          setLoadingImage(false);
        });
    }
  }, [hotel.image_url]);

  const renderHotelContent = () => {
    if (!hotel.name) {
      return (
        <Text style={styles.noDataText}>No hotel information available</Text>
      );
    }

    const imageUrl = hotel.image_url && hotel.image_url.startsWith('http') && !imageError ? hotel.image_url : null;

    return (
      <TouchableOpacity
        style={styles.card}
        activeOpacity={0.9}
        onPress={() => openBookingLink(hotel.name)}
        accessibilityLabel={`View booking options for ${hotel.name || 'Unknown Hotel'}`}
      >
        {loadingImage ? (
          <View style={styles.imagePlaceholder}>
            <ActivityIndicator size="small" color={Colors.PRIMARY} />
          </View>
        ) : (
          <Image
            source={imageUrl ? { uri: imageUrl } : fallbackImage}
            style={styles.image}
            resizeMode="cover"
            onError={(e) => {
              console.log('Image load error for', hotel.name, ':', e.nativeEvent.error);
              setImageError(true);
            }}
            onLoad={() => setLoadingImage(false)}
          />
        )}

        <View style={styles.cardContent}>
          <Text numberOfLines={1} style={styles.hotelName}>
            {hotel.name || 'Unknown Hotel'}
          </Text>
          <Text numberOfLines={2} style={styles.description}>
            {hotel.address || 'No address available'}
          </Text>
          <View style={styles.detailsRow}>
            <Text style={styles.rating}>⭐ {hotel.rating || 'N/A'}</Text>
            <Text style={styles.price}>🪙 {hotel.price_per_night || 'N/A'}</Text>
          </View>
          <TouchableOpacity
            style={styles.bookButton}
            onPress={() => openBookingLink(hotel.name)}
            activeOpacity={0.7}
            accessibilityLabel={`Book ${hotel.name || 'this hotel'} now`}
          >
            <Text style={styles.bookButtonText}>Book Now</Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.header}>🏨 Hotel Recommendation</Text>
      {renderHotelContent()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: 20,
    paddingHorizontal: 10,
  },
  header: {
    fontFamily: 'outfit-bold',
    fontSize: 24,
    color: Colors.PRIMARY,
    marginBottom: 15,
  },
  card: {
    width: 220,
    backgroundColor: Colors.WHITE,
    borderRadius: 15,
    overflow: 'hidden',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 1, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  imagePlaceholder: {
    width: '100%',
    height: 140,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.LIGHT_GRAY,
  },
  image: {
    width: '100%',
    height: 140,
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
  },
  cardContent: {
    padding: 12,
  },
  hotelName: {
    fontFamily: 'outfit-medium',
    fontSize: 16,
    color: Colors.PRIMARY,
    marginBottom: 6,
  },
  description: {
    fontFamily: 'outfit',
    fontSize: 12,
    color: Colors.GRAY,
    lineHeight: 16,
    marginBottom: 8,
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  rating: {
    fontFamily: 'outfit',
    fontSize: 13,
    color: Colors.GRAY,
  },
  price: {
    fontFamily: 'outfit',
    fontSize: 13,
    color: Colors.GRAY,
  },
  bookButton: {
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 8,
    borderRadius: 10,
    alignItems: 'center',
  },
  bookButtonText: {
    fontFamily: 'outfit-medium',
    fontSize: 14,
    color: Colors.WHITE,
  },
  noDataText: {
    fontFamily: 'outfit',
    fontSize: 16,
    color: Colors.GRAY,
    textAlign: 'center',
  },
});
import { View, Text, TouchableOpacity, Dimensions, ScrollView } from 'react-native';
import React, { useContext, useEffect } from 'react';
import { useNavigation } from '@react-navigation/native';
import { useRouter } from 'expo-router';
import { Colors } from '../../constants/Colors';
import { CreateTripContext } from '../../Context/CreateTripContext';
import moment from 'moment';

const { width, height } = Dimensions.get('window');

export default function ReviewTrip() {
    const navigation = useNavigation();
    const router = useRouter();
    const { tripData } = useContext(CreateTripContext);

    useEffect(() => {
        navigation.setOptions({
            headerShown: true,
            headerTransparent: true,
            headerTitle: '',
            headerStyle: {
                borderBottomWidth: 0,
            },
        });
    }, [navigation]);

    const InfoSection = ({ emoji, title, subtitle, value }) => (
        <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginVertical: height * 0.02,
            padding: width * 0.03,
            backgroundColor: Colors.WHITE,
            borderRadius: 12,
            elevation: 2,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
        }}>
            <Text style={{ fontSize: Math.min(width * 0.08, 30), marginRight: width * 0.04 }}>
                {emoji}
            </Text>
            <View style={{ flex: 1 }}>
                <Text style={{
                    fontFamily: 'outfit-regular',
                    fontSize: Math.min(width * 0.045, 18),
                    color: Colors.GRAY,
                }}>
                    {title}
                </Text>
                <Text style={{
                    fontFamily: 'outfit-medium',
                    fontSize: Math.min(width * 0.05, 20),
                    color: Colors.DARK,
                }}>
                    {value}
                </Text>
                {subtitle && (
                    <Text style={{
                        fontFamily: 'outfit-regular',
                        fontSize: Math.min(width * 0.04, 16),
                        color: Colors.GRAY,
                    }}>
                        {subtitle}
                    </Text>
                )}
            </View>
        </View>
    );

    return (
        <View style={{
            flex: 1,
            backgroundColor: Colors.LIGHT_GRAY,
            paddingHorizontal: width * 0.06,
            paddingTop: height * 0.1,
        }}>
            <Text style={{
                fontFamily: 'outfit-bold',
                fontSize: Math.min(width * 0.09, 35),
                color: Colors.DARK,
                marginBottom: height * 0.03,
            }}>
                Review Your Trip
            </Text>

            <Text style={{
                fontFamily: 'outfit-medium',
                fontSize: Math.min(width * 0.045, 18),
                color: Colors.GRAY,
                marginBottom: height * 0.03,
            }}>
                Please confirm your selections below
            </Text>

            <ScrollView
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{
                    paddingBottom: height * 0.15,
                }}
            >
                <InfoSection
                    emoji="📍"
                    title="Destination"
                    value={tripData?.locationInfo?.name || 'Not selected'}
                />

                {tripData?.fromLocation && (
                    <InfoSection
                        emoji="🚗"
                        title="Starting Point"
                        value={tripData?.fromLocation?.name || 'Not specified'}
                        subtitle={tripData?.distance?.value ? `Distance: ${tripData.distance.value} ${tripData.distance.unit || 'km'}` : 'Distance calculation not available'}
                    />
                )}

                <InfoSection
                    emoji="🗓"
                    title="Travel Dates"
                    value={`${moment(tripData?.startDate).format('DD MMM YYYY')} - ${moment(tripData?.endDate).format('DD MMM YYYY')}`}
                    subtitle={tripData?.totalNoOfDays ? `${tripData.totalNoOfDays} Days` : ''}
                />

                <InfoSection
                    emoji="✈️"
                    title="Travelers"
                    value={tripData?.traveler?.title || 'Not selected'}
                />

                <InfoSection
                    emoji="💰"
                    title="Budget"
                    value={tripData?.budget || 'Not selected'}
                />
            </ScrollView>

            <TouchableOpacity
                onPress={() => router.replace('/create-trip/generate-trip')}
                style={{
                    position: 'absolute',
                    bottom: height * 0.03,
                    left: width * 0.06,
                    right: width * 0.06,
                    paddingVertical: height * 0.02,
                    backgroundColor: Colors.PRIMARY,
                    borderRadius: 15,
                    elevation: 5,
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 2 },
                    shadowOpacity: 0.2,
                    shadowRadius: 4,
                }}
            >
                <Text style={{
                    textAlign: 'center',
                    color: Colors.WHITE,
                    fontFamily: 'outfit-medium',
                    fontSize: Math.min(width * 0.05, 20),
                }}>
                    Build My Trip
                </Text>
            </TouchableOpacity>
        </View>
    );
}
import React, { useEffect, useState } from 'react';
import { View, StyleSheet, Platform, Text } from 'react-native';
import { WebView } from 'react-native-webview';

const MapboxGLMap = ({ fromCoordinates, toCoordinates, fromName, toName, mapboxToken }) => {
  const [mapLoaded, setMapLoaded] = useState(false);
  const [loadError, setLoadError] = useState(false);

  // Validate inputs
  if (!fromCoordinates || !toCoordinates || !Array.isArray(fromCoordinates) || !Array.isArray(toCoordinates) ||
      fromCoordinates.length !== 2 || toCoordinates.length !== 2 ||
      !mapboxToken) {
    console.error('MapboxGLMap: Invalid or missing coordinates or token', {
      fromCoordinates,
      toCoordinates,
      hasToken: !!mapboxToken
    });
    return (
      <View style={styles.container}>
        <View style={styles.placeholder}>
          <Text>Cannot display map: Missing required trip details</Text>
        </View>
      </View>
    );
  }

  // Only use this component on web platform
  if (Platform.OS !== 'web') {
    return (
      <View style={styles.container}>
        <View style={styles.placeholder}>
          <Text>Interactive map available on web platform only</Text>
        </View>
      </View>
    );
  }

  // Calculate distance between points using Haversine formula for better zoom calculation
  const calculateDistance = (lon1, lat1, lon2, lat2) => {
    // Radius of earth in km
    const R = 6371;
    
    // Convert degrees to radians
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const radLat1 = lat1 * Math.PI / 180;
    const radLat2 = lat2 * Math.PI / 180;
    
    // Haversine formula
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(radLat1) * Math.cos(radLat2) *
              Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c;
    
    return distance;
  };

  // Calculate appropriate zoom level based on distance between points
  const calculateZoomLevel = () => {
    const lon1 = fromCoordinates[0];
    const lat1 = fromCoordinates[1];
    const lon2 = toCoordinates[0];
    const lat2 = toCoordinates[1];
    
    const distance = calculateDistance(lon1, lat1, lon2, lat2);
    
    // Determine zoom level based on distance with optimized values
    if (distance < 1) return 15;       // Very close - high zoom
    if (distance < 5) return 13;       // Close
    if (distance < 20) return 11;      // Medium distance
    if (distance < 50) return 9;       // Medium-far
    if (distance < 200) return 7;      // Far
    if (distance < 500) return 5;      // Very far
    return 4;                          // Extreme distance
  };

  // HTML content for the WebView with Mapbox GL JS
  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https://*.mapbox.com https://api.mapbox.com">
      <title>Mapbox GL JS Map</title>
      <script src='https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js'></script>
      <link href='https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css' rel='stylesheet' />
      <style>
        body { margin: 0; padding: 0; }
        #map { position: absolute; top: 0; bottom: 0; width: 100%; height: 100%; }
        .marker-container {
          cursor: pointer;
          width: 35px;
          height: 35px;
          position: relative;
        }
        .marker-base {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          box-shadow: 0 0 10px rgba(0,0,0,0.3);
          border: 3px solid white;
          position: absolute;
          top: 0;
          left: 0;
        }
        .marker-label {
          position: absolute;
          color: white;
          font-family: 'Arial', sans-serif;
          font-weight: bold;
          font-size: 16px;
          text-align: center;
          width: 100%;
          top: 50%;
          left: 0;
          transform: translateY(-50%);
          text-shadow: 0 0 3px rgba(0,0,0,0.5);
        }
        .popup-content {
          padding: 10px;
          min-width: 180px;
        }
        .popup-title {
          margin: 0 0 5px 0;
          font-size: 16px;
          font-family: 'Arial', sans-serif;
          font-weight: bold;
        }
        .popup-text {
          margin: 0;
          font-family: 'Arial', sans-serif;
          font-weight: normal;
        }
        .start-title { color: #3FB1AF; }
        .end-title { color: #FF4757; }
        .loading-container {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(255,255,255,0.8);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 100;
        }
        .loading-spinner {
          width: 50px;
          height: 50px;
          border: 5px solid #f3f3f3;
          border-top: 5px solid #3FB1AF;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        .error-message {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(255,255,255,0.9);
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          z-index: 100;
          padding: 20px;
          text-align: center;
        }
        .error-icon {
          font-size: 40px;
          margin-bottom: 10px;
          color: #FF4757;
        }
        .error-title {
          font-family: 'Arial', sans-serif;
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 5px;
          color: #333;
        }
        .error-subtitle {
          font-family: 'Arial', sans-serif;
          font-size: 14px;
          color: #666;
        }
        .zoom-controls {
          position: absolute;
          top: 10px;
          right: 10px;
          display: flex;
          flex-direction: column;
          z-index: 5;
        }
        .zoom-button {
          background-color: white;
          border: none;
          width: 30px;
          height: 30px;
          border-radius: 4px;
          margin-bottom: 5px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 18px;
          cursor: pointer;
          box-shadow: 0 0 5px rgba(0,0,0,0.2);
        }
      </style>
    </head>
    <body>
      <div id="map"></div>
      <div id="loadingIndicator" class="loading-container">
        <div class="loading-spinner"></div>
      </div>
      <div id="errorMessage" class="error-message" style="display: none;">
        <div class="error-icon">⚠️</div>
        <div class="error-title">Map Load Error</div>
        <div class="error-subtitle">Could not load the map. Please try again later.</div>
      </div>
      <script>
        // Initialize the map
        const mapToken = "${mapboxToken}";
        mapboxgl.accessToken = mapToken;
        
        // Parse coordinates and ensure they are valid numbers
        const fromCoordinates = [${parseFloat(fromCoordinates[0])}, ${parseFloat(fromCoordinates[1])}];
        const toCoordinates = [${parseFloat(toCoordinates[0])}, ${parseFloat(toCoordinates[1])}];
        const fromName = "${fromName || 'Starting point'}";
        const toName = "${toName || 'Destination'}";

        // Calculate center point
        const centerLon = (fromCoordinates[0] + toCoordinates[0]) / 2;
        const centerLat = (fromCoordinates[1] + toCoordinates[1]) / 2;

        // Calculate distance for dynamic zoom level
        function calculateDistance(lon1, lat1, lon2, lat2) {
          // Radius of earth in km
          const R = 6371;
          
          // Convert degrees to radians
          const dLat = (lat2 - lat1) * Math.PI / 180;
          const dLon = (lon2 - lon1) * Math.PI / 180;
          const radLat1 = lat1 * Math.PI / 180;
          const radLat2 = lat2 * Math.PI / 180;
          
          // Haversine formula
          const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                    Math.cos(radLat1) * Math.cos(radLat2) *
                    Math.sin(dLon/2) * Math.sin(dLon/2);
          const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
          const distance = R * c;
          
          return distance;
        }

        // Calculate distance between points
        const distance = calculateDistance(
          fromCoordinates[0], 
          fromCoordinates[1], 
          toCoordinates[0], 
          toCoordinates[1]
        );
        
        // Determine appropriate zoom level based on distance
        let initialZoom = 10; // Default
        
        if (distance < 1) initialZoom = 15;       // Very close - high zoom
        else if (distance < 5) initialZoom = 13;  // Close
        else if (distance < 20) initialZoom = 11; // Medium distance
        else if (distance < 50) initialZoom = 9;  // Medium-far
        else if (distance < 200) initialZoom = 7; // Far
        else if (distance < 500) initialZoom = 5; // Very far
        else initialZoom = 4;                     // Extreme distance

        console.log("Distance between points:", distance, "km");
        console.log("Initial zoom level:", initialZoom);

        // Initialize map with optimized default settings
        try {
          const map = new mapboxgl.Map({
            container: 'map',
            style: 'mapbox://styles/mapbox/streets-v11',
            center: [centerLon, centerLat],
            zoom: initialZoom,
            attributionControl: true,
            logoPosition: 'bottom-left',
            pitchWithRotate: false,
            dragRotate: false,
            touchZoomRotate: true,
            maxZoom: 18
          });

          // Add navigation controls
          map.addControl(new mapboxgl.NavigationControl({
            showCompass: false
          }), 'top-right');

          // Create custom HTML elements for markers
          // From marker (green) with label
          function createMarkerElement(label, color) {
            const markerEl = document.createElement('div');
            markerEl.className = 'marker-container';
            
            const markerBase = document.createElement('div');
            markerBase.className = 'marker-base';
            markerBase.style.backgroundColor = color;
            markerEl.appendChild(markerBase);
            
            const markerLabel = document.createElement('div');
            markerLabel.className = 'marker-label';
            markerLabel.textContent = label;
            markerEl.appendChild(markerLabel);
            
            return markerEl;
          }

          // Create marker elements
          const fromEl = createMarkerElement('A', '#3FB1AF');
          const toEl = createMarkerElement('B', '#FF4757');

          // Custom popups
          const fromPopupHTML = '<div class="popup-content"><h3 class="popup-title start-title">From</h3><p class="popup-text">' + fromName + '</p></div>';
          const toPopupHTML = '<div class="popup-content"><h3 class="popup-title end-title">To</h3><p class="popup-text">' + toName + '</p></div>';

          // Add markers with popups
          const fromMarker = new mapboxgl.Marker(fromEl)
            .setLngLat(fromCoordinates)
            .setPopup(new mapboxgl.Popup({
              offset: 25,
              closeButton: true,
              className: 'custom-popup'
            }).setHTML(fromPopupHTML))
            .addTo(map);

          const toMarker = new mapboxgl.Marker(toEl)
            .setLngLat(toCoordinates)
            .setPopup(new mapboxgl.Popup({
              offset: 25,
              closeButton: true,
              className: 'custom-popup'
            }).setHTML(toPopupHTML))
            .addTo(map);

          // Wait for map to load before adding features
          map.on('load', () => {
            // Hide loading indicator
            document.getElementById('loadingIndicator').style.display = 'none';
            
            // Add a line between the points with improved styling
            map.addSource('route', {
              'type': 'geojson',
              'data': {
                'type': 'Feature',
                'properties': {},
                'geometry': {
                  'type': 'LineString',
                  'coordinates': [
                    fromCoordinates,
                    toCoordinates
                  ]
                }
              }
            });

            // Add the route line with enhanced styling
            map.addLayer({
              'id': 'route',
              'type': 'line',
              'source': 'route',
              'layout': {
                'line-join': 'round',
                'line-cap': 'round'
              },
              'paint': {
                'line-color': '#3FB1AF',
                'line-width': 5,
                'line-opacity': 0.8,
                'line-dasharray': [0.5, 1.5] // Optional: creates a dashed line effect
              }
            });

            // Add a buffer around the line for easier hover interaction
            map.addLayer({
              'id': 'route-hover',
              'type': 'line',
              'source': 'route',
              'layout': {
                'line-join': 'round',
                'line-cap': 'round'
              },
              'paint': {
                'line-color': '#3FB1AF',
                'line-width': 12,
                'line-opacity': 0
              }
            });

            // Show tooltips on hover
            map.on('mouseenter', 'route-hover', () => {
              map.getCanvas().style.cursor = 'pointer';
              
              // Optional: make the line slightly thicker on hover
              map.setPaintProperty('route', 'line-width', 7);
            });

            map.on('mouseleave', 'route-hover', () => {
              map.getCanvas().style.cursor = '';
              
              // Reset line width
              map.setPaintProperty('route', 'line-width', 5);
            });

            // Fit bounds to show both markers with padding
            const bounds = new mapboxgl.LngLatBounds()
              .extend(fromCoordinates)
              .extend(toCoordinates);

            map.fitBounds(bounds, {
              padding: {top: 50, bottom: 50, left: 50, right: 50},
              maxZoom: 15
            });

            // Show popups on click
            fromMarker.getElement().addEventListener('click', () => {
              fromMarker.togglePopup();
            });

            toMarker.getElement().addEventListener('click', () => {
              toMarker.togglePopup();
            });
            
            // Create custom zoom controls
            const zoomControls = document.createElement('div');
            zoomControls.className = 'zoom-controls';
            
            const zoomInButton = document.createElement('button');
            zoomInButton.className = 'zoom-button';
            zoomInButton.innerHTML = '+';
            zoomInButton.addEventListener('click', () => {
              map.zoomIn();
            });
            
            const zoomOutButton = document.createElement('button');
            zoomOutButton.className = 'zoom-button';
            zoomOutButton.innerHTML = '−';
            zoomOutButton.addEventListener('click', () => {
              map.zoomOut();
            });
            
            const resetButton = document.createElement('button');
            resetButton.className = 'zoom-button';
            resetButton.innerHTML = '⟲';
            resetButton.addEventListener('click', () => {
              map.fitBounds(bounds, {
                padding: {top: 50, bottom: 50, left: 50, right: 50},
                maxZoom: 15
              });
            });
            
            zoomControls.appendChild(zoomInButton);
            zoomControls.appendChild(zoomOutButton);
            zoomControls.appendChild(resetButton);
            
            document.getElementById('map').appendChild(zoomControls);
          });

          // Error handling for map load
          map.on('error', (e) => {
            console.error('Map error:', e);
            document.getElementById('loadingIndicator').style.display = 'none';
            document.getElementById('errorMessage').style.display = 'flex';
          });
        } catch (error) {
          console.error('Error initializing map:', error);
          document.getElementById('loadingIndicator').style.display = 'none';
          document.getElementById('errorMessage').style.display = 'flex';
        }
      </script>
    </body>
    </html>
  `;

  return (
    <View style={styles.container}>
      <WebView
        originWhitelist={['*']}
        source={{ html: htmlContent }}
        style={styles.webview}
        javaScriptEnabled={true}
        domStorageEnabled={false}
        startInLoadingState={true}
        onError={(e) => {
          console.error('WebView error:', e.nativeEvent);
          setLoadError(true);
        }}
        cacheEnabled={false}
        incognito={true}
        thirdPartyCookiesEnabled={false}
        sharedCookiesEnabled={false}
        onLoadStart={() => console.log('Map WebView loading started')}
        onLoad={() => {
          console.log('Map WebView loaded successfully');
          setMapLoaded(true);
        }}
        onShouldStartLoadWithRequest={(request) => {
          // Only allow loading from mapbox domains and local content
          const shouldLoad = request.url.startsWith('data:') ||
                 request.url.includes('mapbox.com') ||
                 request.url.includes('mapbox-gl-js');
          console.log('WebView load request:', request.url.substring(0, 50) + '...', shouldLoad ? 'ALLOWED' : 'BLOCKED');
          return shouldLoad;
        }}
      />
      
      {loadError && (
        <View style={styles.errorOverlay}>
          <Text style={styles.errorText}>Failed to load map</Text>
          <Text style={styles.errorSubtext}>Please check your connection and try again</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    height: '100%',
    overflow: 'hidden',
    borderRadius: 12,
    position: 'relative',
  },
  webview: {
    flex: 1,
    width: '100%',
    height: '100%',
    backgroundColor: '#f5f5f5',
  },
  placeholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  errorOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ff4757',
    marginBottom: 8,
  },
  errorSubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
});

export default MapboxGLMap;
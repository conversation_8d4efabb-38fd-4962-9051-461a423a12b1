import { View, Text, TouchableOpacity, Image, ActivityIndicator, StyleSheet } from 'react-native';
import React, { useEffect, useState } from 'react';
import { Colors } from '../../constants/Colors'; // Adjust path if needed
import Ionicons from '@expo/vector-icons/Ionicons';
import * as Linking from 'expo-linking';

export default function PlannedTrip({ details }) {
  const [imageMap, setImageMap] = useState({});
  const [loadingMap, setLoadingMap] = useState({});

  console.log('PlannedTrip received details:', details);

  const itinerary = details || [];

  useEffect(() => {
    const fetchImages = async () => {
      const unsplashAccessKey = process.env.EXPO_PUBLIC_UNSPLASH_API_KEY;
      if (!unsplashAccessKey) {
        console.warn('Unsplash API key missing');
        return;
      }

      for (const dayPlan of itinerary) {
        for (const activity of dayPlan.activities) {
          const activityName = activity.activity;
          const location = 'Shimla, HP, India'; // Hardcoded from your database; could be dynamic
          const query = `${activityName}, ${location}`;

          if (!imageMap[activityName]) {
            setLoadingMap(prev => ({ ...prev, [activityName]: true }));
            try {
              const response = await fetch(
                `https://api.unsplash.com/photos/random?query=${encodeURIComponent(query)}&client_id=${unsplashAccessKey}`
              );
              if (!response.ok) throw new Error(`HTTP ${response.status}`);
              const data = await response.json();
              const url = data?.urls?.regular || activity.image_url || null;
              setImageMap(prev => ({ ...prev, [activityName]: url }));
            } catch (error) {
              console.error(`Error fetching image for "${query}":`, error);
              setImageMap(prev => ({ ...prev, [activityName]: null }));
            } finally {
              setLoadingMap(prev => ({ ...prev, [activityName]: false }));
            }
          }
        }
      }
    };

    if (itinerary.length > 0) {
      fetchImages();
    }
  }, [details]);

  const renderNavigateButton = (item) => (
    item.geo_coordinates && (
      <TouchableOpacity
        style={styles.navigateButton}
        onPress={() => {
          const lat = item.geo_coordinates.latitude;
          const lon = item.geo_coordinates.longitude;
          if (lat && lon) {
            Linking.openURL(`https://www.openstreetmap.org/directions?route=${lat},${lon}`);
          } else {
            console.warn('Coordinates not available');
          }
        }}
      >
        <Text><Ionicons name="navigate" size={20} color={Colors.WHITE} /></Text>
      </TouchableOpacity>
    )
  );

  return (
    <View style={styles.container}>
      <Text style={styles.header}>🏕️ Plan Details</Text>

      {itinerary.length > 0 ? (
        itinerary.map((dayPlan, index) => (
          <View key={index} style={styles.dayContainer}>
            <Text style={styles.dayTitle}>Day {dayPlan.day || index + 1}</Text>
            {dayPlan.activities.map((item, activityIndex) => (
              <View key={activityIndex} style={styles.activityCard}>
                {loadingMap[item.activity] ? (
                  <View style={styles.imagePlaceholder}>
                    <ActivityIndicator size="large" color={Colors.PRIMARY} />
                  </View>
                ) : (
                  <Image
                    source={
                      imageMap[item.activity]
                        ? { uri: imageMap[item.activity] }
                        : require('../../assets/images/icon.png')
                    }
                    style={styles.activityImage}
                  />
                )}
                <View style={styles.activityContent}>
                  <Text style={styles.activityName}>{item.activity || 'Unnamed Activity'}</Text>
                  <Text style={styles.activityDescription}>
                    {item.description || 'No description available'}
                  </Text>
                  <View style={styles.infoRow}>
                    <Text style={styles.activityInfo}>
                      ⏰ Time to Spend: {item.duration || 'N/A'}
                    </Text>
                  </View>
                  <View style={styles.infoRow}>
                    <Text style={styles.activityInfo}>
                      📅 Best Time: {item.best_time_to_visit || 'Not specified'}
                    </Text>
                  </View>
                  <View style={styles.infoRow}>
                    <Text style={styles.activityInfo}>
                      💵 Ticket Price: {item.ticket_pricing || 'Free'}
                    </Text>
                    {renderNavigateButton(item)}
                  </View>
                </View>
              </View>
            ))}
          </View>
        ))
      ) : (
        <Text style={styles.noDataText}>No plan details available</Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: 20,
    paddingHorizontal: 15,
    paddingBottom: 20,
  },
  header: {
    fontSize: 24,
    fontFamily: 'outfit-bold',
    color: Colors.PRIMARY,
    marginBottom: 20,
    textAlign: 'center',
  },
  dayContainer: {
    marginBottom: 25,
  },
  dayTitle: {
    fontSize: 20,
    fontFamily: 'outfit-medium',
    color: Colors.PRIMARY,
    marginBottom: 15,
  },
  activityCard: {
    backgroundColor: Colors.LIGHT_GRAY,
    borderRadius: 15,
    marginBottom: 15,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  imagePlaceholder: {
    height: 150,
    backgroundColor: Colors.WHITE,
    justifyContent: 'center',
    alignItems: 'center',
  },
  activityImage: {
    width: '100%',
    height: 150,
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    resizeMode: 'cover',
  },
  activityContent: {
    padding: 15,
  },
  activityName: {
    fontSize: 18,
    fontFamily: 'outfit-medium',
    color: Colors.DARK,
    marginBottom: 8,
  },
  activityDescription: {
    fontSize: 14,
    fontFamily: 'outfit',
    color: Colors.GRAY,
    lineHeight: 20,
    marginBottom: 10,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  activityInfo: {
    fontSize: 14,
    fontFamily: 'outfit',
    color: Colors.DARK_GRAY,
  },
  navigateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  navigateText: {
    marginLeft: 5,
    color: Colors.WHITE,
    fontFamily: 'outfit-medium',
    fontSize: 14,
  },
  noDataText: {
    fontSize: 16,
    fontFamily: 'outfit',
    color: Colors.GRAY,
    textAlign: 'center',
    marginTop: 20,
  },
});